import copy
import os
import json
import sys
import time
import ast
from collections import defaultdict
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import requests
import re
import traceback
import inspect
from datetime import datetime, timedelta
import ipaddress


from src.config_loader import cmdb_config
from src.record_config import SyncRecord, SyncRecordManager
from src.cipher.cipher_util import CipherUtil
from src.validators import FilterValidator
# from src.logger_config import logger

# 确保日志配置已设置
# setup_logging()
import logging
logger = logging.getLogger(__name__)

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

baseDir = Path(rootPath)


class SyncManager:
    """
    同步管理器，负责处理数据同步流程
    """

    def __init__(self):
        """初始化同步管理器"""
        self.base_config = cmdb_config.get('base', {})
        self.endpoints = cmdb_config.get('endpoints', {})
        self.transformers = cmdb_config.get('transformers', {})
        self.models = cmdb_config.get('models', {})
        # 新增访问https打印warning
        ssl_dir = baseDir / self.base_config.get('global', {}).get('ssl_dir', './config/ssl_dir')
        self.cert = (f"{ssl_dir}/server.crt", f"{ssl_dir}/server.key")
        # 缓存目录
        self.cache_dir = baseDir / self.base_config.get('global', {}).get('cache_dir', './cache')
        os.makedirs(self.cache_dir, exist_ok=True)
        self.sync_direction = 'cloud_to_internal'
        # 初始化API客户端
        self.api_clients = self._init_api_clients()

        # 数据存储
        self.cloud_data = {}  # 存储云内侧数据
        self.internal_data = {}  # 存储行内侧数据
        self.history_data = {}  # 存储历史数据
        self.sync_data = defaultdict(lambda: defaultdict(list))   # 存储历史同步的数据
        self.last_sync_times = {}  # 存储上次同步时间
        self.application_code = {}  # 存储应用系统Code
        self.internal_related_data = {} # 存储行内关联模型数据
        self.internal_application_system_data = [] # 存储行内所有的应用系统信息
        self.retry_error_update_datas = [] # 批量操作报错数据，收集封装后的请求数据
        self.retry_error_delete_datas = [] # 批量删除操作数据报错 收集

        # 当前处理的模型ID
        self.current_model_id = None

        # 加载模型特有的转换函数
        self._load_model_transformers()

        # 尝试从缓存中加载数据
        self._load_data_from_cache()

        # 初始化同步记录管理器
        self.record_manager = SyncRecordManager()

    def _load_model_transformers(self):
        """加载模型特有的转换函数"""
        for model_id, model_config in self.models.items():
            if 'transformers' in model_config:
                model_transformers = model_config['transformers']
                # 将模型特有的转换函数添加到全局转换函数中
                for key, value in model_transformers.items():
                    self.transformers[key] = value
                    logger.debug(f"Loaded model-specific transformer: {key} for model {model_id}")

                    # 如果是Python代码转换函数，预编译函数
                    if value.get('type') == 'python' and 'code' in value:
                        try:
                            code = value['code']
                            local_vars = {}
                            exec(code, globals(), local_vars)

                            # 获取函数名
                            func_name = next(iter(local_vars))
                            logger.debug(f"Compiled Python transformer: {func_name}")
                        except Exception as e:
                            logger.error(f"Failed to compile Python transformer {key}: {e}")

    def _init_api_clients(self) -> Dict[str, Any]:
        """初始化API客户端"""
        api_clients = {}
        apis_config = self.base_config.get('apis', {})

        # 初始化云内侧OC API客户端（多个数据源）
        cloud_side_configs = apis_config.get('cloud_side', {})
        for source_id, source_config in cloud_side_configs.items():
            api_clients[f"cloud_{source_id}"] = {
                'config': source_config,
                'session': requests.Session(),
                'token': None,
                'token_expires': 0
            }

            # 设置默认请求头
            headers = source_config.get('headers', {})
            api_clients[f"cloud_{source_id}"]['session'].headers.update(headers)
            logger.info(f"Initialized cloud API client for source: {source_id}")
        # 初始化云内的SC侧的API 客户端(多个数据源)
        sc_cloud_side_configs = apis_config.get('sc_cloud_side', {})
        for source_id, source_config in sc_cloud_side_configs.items():
            api_clients[f"sc_cloud_{source_id}"] = {
                'config': source_config,
                'session': requests.Session(),
                'token': None,
                'token_expires': 0
            }

            # 设置默认请求头
            headers = source_config.get('headers', {})
            api_clients[f"sc_cloud_{source_id}"]['session'].headers.update(headers)
            logger.info(f"Initialized cloud API client for source: {source_id}")
        # 初始化行内侧API客户端
        internal_config = apis_config.get('internal_side', {})
        if internal_config:
            api_clients['internal_side'] = {
                'config': internal_config,
                'session': requests.Session(),
                'token': None,
                'token_expires': 0
            }

            # 设置默认请求头
            headers = internal_config.get('headers', {})
            api_clients['internal_side']['session'].headers.update(headers)
            logger.info("Initialized internal API client")

        return api_clients

    def initialize_sync(self, force_refresh=False):
        """
        初始化同步过程，包括全量获取数据和模型同步

        Args:
            force_refresh: 是否强制刷新，不使用缓存
        """
        logger.info("Starting initialization sync process")

        sync_records = []

        # 首次同步或强制刷新时，使用全量同步
        if force_refresh:
            # 1. 全量获取云内侧数据
            self.fetch_all_cloud_data(force_refresh=force_refresh)

            # 2. 全量获取行内侧数据
            self.fetch_all_internal_data(force_refresh=force_refresh)

            # 3. 根据模型配置进行同步
            sync_records = self.sync_all_models()
        else:
            # 先加载缓存数据
            self._load_data_from_cache()

            # 对每个模型执行增量同步
            for model_id, model_config in self.models.items():
                if model_config.get('enabled', True) and model_config.get('sync_enabled', True):
                    try:
                        logger.info(f"Incremental sync not enabled for model: {model_id}, using full sync")
                        # 全量获取数据
                        self.fetch_all_cloud_data(force_refresh, model_id=model_id)
                        self.fetch_all_internal_data(force_refresh, model_id=model_id)
                        # 同步模型
                        sync_record = self.sync_model(model_id, model_config)
                        sync_records.append(sync_record)
                    except Exception as e:
                        logger.error(f"Error syncing model {model_id}: {str(e)}")
                        logger.debug(f"Exception details: {traceback.format_exc()}")
                        # 记录同步失败
                        sync_direction = self._determine_sync_direction(model_config)
                        sync_record = SyncRecord(model_id, 'full', sync_direction)
                        sync_record.errors.append({
                            'error_type': 'sync_error',
                            'error_message': str(e),
                            'time': datetime.now(),
                            'record_id': None,
                            'stack_trace': traceback.format_exc()
                        })
                        sync_record.complete('failed')
                        record_manager = self.record_manager
                        record_manager.save_record(sync_record)
                        sync_records.append(sync_record)
        logger.info("Initialization sync process completed")
        return sync_records

    def _load_data_from_cache(self):
        """从缓存中加载数据"""
        logger.info("Loading data from cache")

        # 初始化历史数据存储
        self.history_data = {}

        # 加载云内侧数据
        # for model_id in self.models.keys():
        #     cache_key = f"cloud_{model_id}"
        #     cached_data = self._load_from_cache(cache_key)
        #     if cached_data:
        #         self.cloud_data[model_id] = cached_data
        #         logger.info(f"Loaded cloud-side data for model {model_id} from cache")

        # 加载行内侧数据
        for model_id in self.models.keys():
            cache_key = f"internal_{model_id}"
            cached_data = self._load_from_cache(cache_key)
            if cached_data:
                self.internal_data[model_id] = cached_data
                logger.info(f"Loaded internal-side data for model {model_id} from cache")
            # 加载行内关联数据
            cache_internal_related_key = f"internal_related_{model_id}"
            cache_internal_related_data = self._load_from_cache(cache_internal_related_key)
            if cache_internal_related_data:
                self.internal_related_data[model_id] = cache_internal_related_data
                logger.info(f"Loaded internal related data for model {model_id} from cache")

        # 加载历史数据缓存
        for model_id in self.models.keys():
            cache_key = f"history_{model_id}"
            cached_data = self._load_from_cache(cache_key)
            if cached_data:
                self.history_data[model_id] = cached_data
                logger.info(f"Loaded historical data for model {model_id} from cache")

    def _process_related_models(self, model_id: str, related_models: List[Dict[str, Any]],
                                primary_data: List[Dict[str, Any]], source_id: str = None) -> Dict[
        str, List[Dict[str, Any]]]:
        """处理关联模型数据

        Args:
            model_id: 主模型ID
            related_models: 关联模型配置列表
            primary_data: 主模型数据
            source_id: 数据源ID

        Returns:
            关联模型数据字典，键为模型名称，值为模型数据
        """
        related_data = {}

        if not related_models:
            return related_data

        logger.info(f"Processing related models for {model_id}" +
                    (f" from source {source_id}" if source_id else ""))

        for related_model in related_models:
            try:
                model_name = related_model.get('model')
                relation_method = related_model.get('relation_method', 'direct')

                if not model_name:
                    continue

                logger.info(f"Processing related model {model_name} using method {relation_method}")

                # 根据关联方式处理
                if relation_method == 'relation_table':
                    # 通过关系表关联的模型
                    relation_data = self._fetch_relation_table_data(model_id, related_model, primary_data, source_id)
                    if relation_data:
                        related_data[model_name] = relation_data
                        logger.info(f"Fetched {len(relation_data)} records for related model {model_name}")
                elif relation_method == 'direct':
                    related_endpoint = related_model.get('endpoint')
                    get_relation_model_key = f"get_{related_model.get('model')}"
                    self._get_endpoint_url(get_relation_model_key, related_model.get('model'))
                    # 直接关联的模型
                    # related_endpoint = related_model.get('endpoint')
                    if not related_endpoint:
                        logger.warning(f"Missing endpoint for related model {model_name}, skipping")
                        continue

                    # 获取关联模型数据
                    model_config = {'batch_size': 1000, 'cloud_side': {}}
                    related_model_data = self._fetch_cloud_data(get_relation_model_key, model_config, source_id)

                    if related_model_data:
                        related_data[model_name] = related_model_data
                        logger.info(f"Fetched {len(related_model_data)} records for related model {model_name}")
                else:
                    logger.warning(f"Unknown relation method: {relation_method} for model {model_name}")

            except Exception as e:
                model_name = related_model.get('model', 'unknown')
                logger.error(f"Error processing related model {model_name} for {model_id}: {str(e)}")
                logger.debug(f"Exception details: {traceback.format_exc()}")
                # 继续处理其他关联模型

        return related_data

    def fetch_all_cloud_data(self, force_refresh=False, model_id=None, source_id=None):
        """全量获取云内侧所有模型数据

        Args:
            force_refresh: 是否强制刷新，不使用缓存
            model_id: 指定模型ID，如果提供则只获取该模型数据
            source_id: 指定数据源ID，如果提供则只使用该数据源
        """
        try:
            if model_id:
                logger.info(f"Fetching cloud-side data for model: {model_id}")
            else:
                logger.info("Fetching all cloud-side data")

            if source_id:
                logger.info(f"Using specified data source: {source_id}")

            # 确定要处理的模型列表
            if model_id:
                if model_id in self.models:
                    models_to_process = {model_id: self.models[model_id]}
                else:
                    logger.warning(f"Model {model_id} not found in configuration, skipping")
                    return
            else:
                models_to_process = self.models

            # 获取所有需要同步的模型的数据
            for current_model_id, model_config in models_to_process.items():
                try:
                    # 检查模型是否启用同步
                    if not model_config.get('enabled', True) or not model_config.get('sync_enabled', True):
                        logger.info(f"Skipping model: {current_model_id} (not enabled for sync)")
                        continue

                    # 检查缓存中是否有数据且不强制刷新
                    if current_model_id in self.cloud_data and not force_refresh:
                        # 新增：即使用缓存，也递归补充eps_id和应用系统信息
                        cloud_side_config = model_config.get('cloud_side', {})
                        for src_id, src_data in self.cloud_data[current_model_id].items():
                            primary_data = src_data.get('primary', [])
                            eps_ids = [item["epsId"] for item in primary_data if "epsId" in item and item["epsId"] != "0"]
                            application_dict = self.get_application_by_eps_ids(eps_ids, src_id, model_config)
                            self.application_code.update(application_dict)
                        logger.info(f"Using cached data for model: {current_model_id}")
                        continue

                    logger.info(f"Fetching cloud-side data for model: {current_model_id}")

                    # 获取该模型使用的数据源
                    model_sources = self._get_model_sources(current_model_id, source_id)
                    if not model_sources:
                        continue

                    # 初始化模型数据存储
                    self.cloud_data[current_model_id] = {}

                    # 从每个数据源获取数据
                    for src_id in model_sources:
                        client_key = f"cloud_{src_id}"
                        if client_key not in self.api_clients:
                            logger.warning(f"Source {src_id} not configured, skipping")
                            continue

                        logger.info(f"Fetching data for model {current_model_id} from source {src_id}")

                        # 获取配置
                        cloud_side_config = model_config.get('cloud_side', {})
                        primary_model = cloud_side_config.get('primary_model')
                        endpoint = cloud_side_config.get('primary_endpoint')
                        get_cloud_model_key = f"get_{primary_model}"
                        self._get_endpoint_url(get_cloud_model_key, primary_model)
                        if not primary_model or not endpoint:
                            logger.warning(
                                f"Missing primary_model or primary_endpoint for {current_model_id}, skipping")
                            continue

                        # 获取主模型数据
                        data = self._fetch_cloud_data(get_cloud_model_key, model_config, src_id)

                        if not data:
                            logger.warning(
                                f"No data found for model {current_model_id} from source {src_id}, skipping")
                            continue

                        # 处理关联模型数据 - 使用抽取的公共方法
                        related_models = cloud_side_config.get('related_models', [])
                        related_data = self._process_related_models(current_model_id, related_models, data, src_id)

                        # 存储主模型数据和关联数据，按数据源分类
                        self.cloud_data[current_model_id][src_id] = {
                            'primary': data,
                            'related': related_data,
                            'source_id': src_id  # 添加数据源标识
                        }

                        records_count = len(data) if isinstance(data, list) else 1
                        logger.info(
                            f"Completed fetching cloud-side data for model: {current_model_id} from source {src_id} (found {records_count} records)")
                        # 主数据里去取espId  如果ID为0 就不要
                        eps_ids = [item["epsId"] for item in data if "epsId" in item and item["epsId"] != "0"]
                        self.application_code = self.get_application_by_eps_ids(eps_ids, src_id, model_config)
                    # 保存到缓存
                    self._save_to_cache(f"cloud_{current_model_id}", self.cloud_data[current_model_id])

                except Exception as e:
                    logger.error(f"Error fetching cloud-side data for model {current_model_id}: {str(e)}")
                    logger.debug(f"Exception details: {traceback.format_exc()}")
                    # 继续处理其他模型
        except Exception as e:
            logger.error(f"Error in fetch_all_cloud_data: {str(e)}")
            logger.debug(f"Exception details: {traceback.format_exc()}")

    def _get_model_sources(self, model_id, specified_source_id=None):
        """获取模型的数据源列表

        Args:
            model_id: 模型ID
            specified_source_id: 指定的数据源ID

        Returns:
            数据源ID列表
        """
        # 获取该模型使用的数据源
        model_sources = self.base_config.get('model_sources', {}).get(model_id, [])

        # 如果指定了数据源，只使用该数据源
        if specified_source_id:
            if model_sources and specified_source_id not in model_sources:
                logger.warning(
                    f"Specified source {specified_source_id} is not configured for model {model_id}, skipping")
                return []
            return [specified_source_id]

        # 如果没有指定数据源且模型没有配置数据源，使用所有可用的数据源
        if not model_sources:
            cloud_sources = [k.replace('cloud_', '') for k in self.api_clients.keys() if
                             k.startswith('cloud_')]
            if cloud_sources:
                logger.info(
                    f"No specific sources defined for model {model_id}, using all available sources: {cloud_sources}")
                return cloud_sources
            else:
                logger.warning(f"No cloud sources available for model {model_id}")
                return []

        return model_sources

    def fetch_all_internal_data(self, force_refresh=False, model_id=None):
        """全量获取行内侧所有模型数据

        Args:
            force_refresh: 是否强制刷新，不使用缓存
            model_id: 指定模型ID，如果提供则只获取该模型数据
        """
        # 检查缓存中是否有数据且不强制刷新 但是 关联数据没有数据  需要重新更新关联数据
        if model_id in self.internal_data and not force_refresh and model_id not in self.internal_related_data: # 模型关联数据不存在
                model_config = self.models.get(model_id)
                internal_side_config = model_config.get('internal_side', {})
                batch_size = self._get_batch_size(internal_side_config)
                api_config = internal_side_config.get('get_params', {}).get('api_config', {})
                pagination_cfg = api_config.get('pagination', {})
                result_path = api_config.get('result_path', 'data')
                endpoint = internal_side_config.get("get_endpoint")
                main_params = {"isRelatedCIId":True}
                self.get_internal_related_data(internal_side_config,main_params,endpoint,
                                               result_path,pagination_cfg,batch_size,model_id)

        return self._fetch_data(
            data_dict=self.internal_data,
            data_type="internal",
            model_id=model_id,
            force_refresh=force_refresh,
            data_fetcher=self._fetch_internal_data,
            config_key="internal_side",
            primary_key="model",
            endpoint_key="get_endpoint",
            related_key=None,  # 行内数据没有关联模型
            sync_enabled_key="sync_enabled"
        )

    def _fetch_data(self, data_dict, data_type, model_id, force_refresh, data_fetcher,
                    config_key, primary_key, endpoint_key, related_key, sync_enabled_key="enabled"):
        """
        通用数据获取方法，用于获取云内侧或行内侧的数据

        Args:
            data_dict: 存储数据的字典
            data_type: 数据类型描述
            model_id: 指定模型ID
            force_refresh: 是否强制刷新
            data_fetcher: 数据获取函数
            config_key: 配置中的键名
            primary_key: 主模型配置键名
            endpoint_key: 端点配置键名
            related_key: 关联模型配置键名
            sync_enabled_key: 同步启用配置键名
        """
        try:
            if model_id:
                logger.info(f"Fetching {data_type}-side data for model: {model_id}")
            else:
                logger.info(f"Fetching all {data_type}-side data")

            # 确定要处理的模型列表
            if model_id:
                if model_id in self.models:
                    models_to_process = {model_id: self.models[model_id]}
                else:
                    logger.warning(f"Model {model_id} not found in configuration, skipping")
                    return
            else:
                models_to_process = self.models

            # 获取所有需要同步的模型的数据
            for current_model_id, model_config in models_to_process.items():
                try:
                    # 检查模型是否启用同步
                    if not model_config.get('enabled', True) or not model_config.get(sync_enabled_key, True):
                        logger.info(f"Skipping model: {current_model_id} (not enabled for sync)")
                        continue

                    # 检查缓存中是否有数据且不强制刷新
                    if current_model_id in data_dict and not force_refresh:
                        logger.info(f"Using cached data for model: {current_model_id}")
                        continue

                    logger.info(f"Fetching {data_type}-side data for model: {current_model_id}")

                    # 获取配置
                    side_config = model_config.get(config_key, {})
                    primary_model = side_config.get(primary_key)
                    endpoint = side_config.get(endpoint_key)

                    if not primary_model or not endpoint:
                        logger.warning(f"Missing {primary_key} or {endpoint_key} for {current_model_id}, skipping")
                        continue

                    # 获取数据
                    data = data_fetcher(endpoint, model_config)

                    if not data:
                        logger.warning(f"No internal data found for model {current_model_id}, skipping")
                        continue

                    # 处理关联模型数据（如果有）
                    if related_key:
                        related_data = {}
                        # 处理所有关联模型
                        for related_model in side_config.get(related_key, []):
                            model_name = related_model.get('model')
                            try:
                                # direct 或 relation_table
                                relation_method = related_model.get('relation_method')

                                if not model_name:
                                    continue

                                # 根据关联方式处理
                                if relation_method == 'relation_table':
                                    # 通过关系表关联的模型
                                    relation_data = self._fetch_relation_table_data(current_model_id, related_model,
                                                                                    data)
                                    if relation_data:
                                        related_data[model_name] = relation_data
                                elif relation_method == 'direct':
                                    # 直接关联的模型
                                    related_endpoint = related_model.get('endpoint')
                                    if not related_endpoint:
                                        continue

                                    # 获取关联模型数据
                                    related_model_data = self._fetch_cloud_data(related_endpoint, model_config)
                                    if related_model_data:
                                        related_data[model_name] = related_model_data
                            except Exception as e:
                                logger.error(
                                    f"Error processing related model {model_name} for {current_model_id}: {str(e)}")
                                logger.debug(f"Exception details: {traceback.format_exc()}")
                                # 继续处理其他关联模型

                        # 存储主模型数据和关联数据
                        data_dict[current_model_id] = {
                            'primary': data,
                            'related': related_data
                        }
                    else:
                        # 只存储主数据（行内数据）
                        data_dict[current_model_id] = data

                    records_count = len(data) if isinstance(data, list) else len(data.get('records', []))
                    logger.info(
                        f"Completed fetching {data_type}-side data for model: {current_model_id} (found {records_count} records)")

                    # 保存到缓存
                    self._save_to_cache(f"{data_type}_{current_model_id}", data_dict[current_model_id])
                except Exception as e:
                    logger.error(f"Error fetching {data_type}-side data for model {current_model_id}: {str(e)}")
                    logger.debug(f"Exception details: {traceback.format_exc()}")
                    # 继续处理其他模型

            if model_id:
                logger.info(f"Completed fetching {data_type}-side data for model: {model_id}")
            else:
                logger.info(f"Completed fetching all {data_type}-side data")

            return data_dict
        except Exception as e:
            logger.error(f"Unexpected error in _fetch_data: {str(e)}")
            logger.debug(f"Exception details: {traceback.format_exc()}")
            raise

    def sync_all_models(self):
        """根据模型配置同步所有模型"""
        logger.info("Starting model synchronization")
        # 先确认一下那些云内客户端是可用的 将不可用的云内客户端剔除 容灾的方案 需要获取全部云内客户端才行


        # 进行同步的时候就查询应用系统的数据
        self.internal_application_system_data = []
        # 查询行内应用系统数据
        self.get_internal_application_system_data()
        # 先在这里对云策略台账进行同步 然后将其同步改为False
        sync_records = []
        sync_record_log = self.sync_cloud_firewall_policy_log()
        if sync_record_log:
            sync_records.append(sync_record_log)
        # 确定模型同步顺序
        model_order = self._determine_model_order()
        # 按顺序同步模型
        for model_id in model_order:
            if model_id in self.base_config.get("skipping"):
                continue
            model_config = self.models.get(model_id)
            if not model_config or not model_config.get('enabled', True) or not model_config.get('sync_enabled', True):
                continue
            #  获取模型云内数据
            self.fetch_all_cloud_data(model_id=model_id)
            # 获取模型行内的数据
            self.fetch_all_internal_data(model_id=model_id)
            logger.info(f"Synchronizing model: {model_id}")
            sync_record = self.sync_model(model_id, model_config)
            sync_records.append(sync_record)
            logger.info(f"Synchronizing model: {model_id}")
        # 最后来同步设备链路表
        device_link_sync_records = self.collect_sys_device_link()
        sync_records.append(device_link_sync_records)
        logger.info("Completed model synchronization")
        return sync_records

    def sync_model(self, model_id: str, model_config: Dict[str, Any], sync_record=None):
        """同步单个模型"""
        # 如果没有传入同步记录，创建一个新的
        if sync_record is None:
            sync_direction = self._determine_sync_direction(model_config)
            sync_record = SyncRecord(model_id, 'full', sync_direction)
            sync_record.source_id = model_config.get('cloud_side', {}).get('source_id')
        # 供测试使用的两行代码，上生产可以删掉,不删掉也没有影响
        if not self.internal_application_system_data:
            self.get_internal_application_system_data()
        # 资源类型
        resource_type = model_config.get('resource_type', 'cloud')

        # 获取云内侧和行内侧数据
        cloud_data_sources = self.cloud_data.get(model_id, {})

        # 记录开始同步
        logger.info(f"Synchronizing model: {model_id}")

        # 获取历史数据(上一周期的数据)
        history_data = self.history_data.get(model_id)
        internal_data = None

        # if history_data and 'internal_data' in history_data:
        if history_data and 'sync_data' in history_data:
            # 使用历史数据作为比较基准
            # internal_data = history_data['internal_data']
            internal_data = history_data['sync_data']
            # 合并所有的同步结构，并附带上数据源标识
            internal_data = self._assembled_sync_data(internal_data, model_id)
            logger.info(f"Using historical data for model {model_id} from previous sync cycle")
        else:
            # 首次同步或历史数据不存在，使用当前行内数据
            internal_data = self.internal_data.get(model_id)
            logger.info(f"No historical data found for model {model_id}, using current internal data")

        if not cloud_data_sources:
            logger.warning(f"No cloud data found for model {model_id}, skipping")
            return

        if not internal_data and resource_type == 'physical':
            logger.warning(f"No internal data found for model {model_id}, skipping")
            return

        # 设置当前模型ID
        self.current_model_id = model_id

        # 合并所有数据源的数据
        assembled_data = self._assembled_cloud_data(cloud_data_sources, model_id, model_config)

        # 获取字段映射配置
        field_mappings = model_config.get('field_mappings', [])

        # 按同步方向分组字段映射
        cloud_to_internal_mappings = []
        internal_to_cloud_mappings = []
        bidirectional_mappings = []

        for mapping in field_mappings:
            sync_direction = mapping.get('sync_direction', 'cloud_to_internal')
            if sync_direction == 'cloud_to_internal':
                cloud_to_internal_mappings.append(mapping)
            elif sync_direction == 'internal_to_cloud':
                internal_to_cloud_mappings.append(mapping)
            elif sync_direction == 'bidirectional':
                bidirectional_mappings.append(mapping)

        # 执行云内到行内的同步（包含双向映射的统计）
        if cloud_to_internal_mappings or bidirectional_mappings:
            # 获取云内最新数据: 首次，缓存内就是最新的 后面：重新查询的是最新的
            self._sync_cloud_to_internal(model_id, model_config, assembled_data, internal_data,
                                         cloud_to_internal_mappings + bidirectional_mappings, sync_record)

        # 执行行内到云内的同步（双向映射不重复统计）
        # if internal_to_cloud_mappings or bidirectional_mappings:
        #     # 获取当前最新的行内数据用于同步
        #     current_internal_data = self.internal_data.get(model_id)
        #     if current_internal_data:
        #         # 创建临时同步记录用于行内到云内同步，避免重复统计双向映射
        #         temp_sync_record = None if bidirectional_mappings else sync_record
        #         self._sync_internal_to_cloud(model_id, model_config, current_internal_data, assembled_data,
        #                                      internal_to_cloud_mappings + bidirectional_mappings, temp_sync_record)
        #     else:
        #         logger.warning(
        #             f"No current internal data found for model {model_id}, skipping internal to cloud sync")

        # 同步完成后，保存当前数据作为历史数据
        self.history_data[model_id] = {
            'cloud_data': self.cloud_data.get(model_id, {}),
            'internal_data': self.internal_data.get(model_id),
            'sync_data': self.sync_data.get(model_id, {}),
            'timestamp': time.time()
        }

        self._save_to_cache(f"history_{model_id}", self.history_data[model_id], expire_time=86400)

        # 同步完成后更新记录状态
        sync_record.complete('success')

        # 保存同步记录
        record_manager = self.record_manager
        record_manager.save_record(sync_record)

        return sync_record

    def _assembled_sync_data(self, internal_data, model_id):

        assembled_sync_data = []

        # 同步数据为空，认为是初次，给现存的行内数据
        if not internal_data:
            return self.internal_data.get(model_id)

        for src_id, items in internal_data.items():
            for item in items:
                item['_source_id'] = src_id
                assembled_sync_data.append(item)

        return assembled_sync_data

    def _assembled_cloud_data(self, cloud_data_sources: Dict[str, any], model_id: str, model_config: Dict[str, any]) -> \
            List[Dict[str, Any]]:
        # 合并所有数据源的数据
        assembled_data = []

        for source_id, source_data in cloud_data_sources.items():
            logger.info(f"Processing data from source {source_id} for model {model_id}")

            # 获取主模型数据和关联数据
            primary_data = source_data.get('primary', [])
            related_data = source_data.get('related', {})

            # 组装数据，将主模型数据和关联数据组合在一起
            source_assembled_data = self._assemble_data(model_id, model_config, primary_data, related_data)

            # 为每个数据项添加数据源标识
            for item in source_assembled_data:
                item['_source_id'] = source_id

            # 添加到总数据集
            assembled_data.extend(source_assembled_data)
        return assembled_data

    def _sync_cloud_to_internal(self, model_id: str, model_config: Dict[str, Any],
                                assembled_data: List[Dict[str, Any]], internal_data: List[Dict[str, Any]],
                                field_mappings: List[Dict[str, Any]], sync_record=None):
        """从云内侧同步到行内侧"""

        logger.info(f"Syncing from cloud to internal for model: {model_id}")
        sync_direction = 'cloud_to_internal'

        # 设置当前模型ID
        self.current_model_id = model_id

        # 更新方向统计
        if sync_record:
            sync_record.cloud_to_internal_count += len(assembled_data)
            # 设置总记录数（只设置一次）
            sync_record.add_total_records(len(assembled_data))

        # 获取配置
        could_side_config = model_config.get("cloud_side", {})
        could_primary_key = could_side_config.get('primary_key')
        internal_side_config = model_config.get('internal_side', {})
        internal_model = internal_side_config.get('model')
        internal_primary_key = internal_side_config.get('primary_key')
        could_match_key = internal_side_config.get('match_key', could_primary_key)
        update_endpoint = internal_side_config.get('update_endpoint')
        delete_endpoint = internal_side_config.get('delete_endpoint')
        batch_size = model_config.get('batch_size', 50)
        resource_type = model_config.get('resource_type', 'cloud')

        if not internal_model or not internal_primary_key or not update_endpoint:
            logger.warning(f"Missing internal model configuration for {model_id}, skipping")
            return

        # 创建行内侧数据索引
        internal_data_index = self._create_internal_data_index(internal_data, internal_primary_key)

        # 处理云内数据并准备更新/创建列表
        update_items, create_items, processed_keys = self._process_cloud_data(
            model_id, model_config, assembled_data, internal_data_index,
            internal_primary_key, field_mappings, resource_type, sync_record
        )

        # 获取需要删除的项目
        delete_items = self._get_deletion_items(model_id, internal_data_index, processed_keys,
                                                resource_type, could_match_key)

        # 获取同步数量限制
        sync_limits = self._get_sync_limits(model_id, sync_direction)

        # 应用操作特定的同步数量限制
        update_items = self._apply_operation_limits(
            update_items, sync_limits['update'], 'update', model_id, sync_direction
        )
        create_items = self._apply_operation_limits(
            create_items, sync_limits['create'], 'create', model_id, sync_direction
        )
        delete_items = self._apply_operation_limits(
            delete_items, sync_limits['delete'], 'delete', model_id, sync_direction
        )

        # 执行批量更新
        if update_items:
            logger.info(f"Batch updating {len(update_items)} items for model: {model_id}")
            self._batch_update_internal_items(update_endpoint, internal_side_config, update_items, sync_record=sync_record, operation_type='updated')
            if self.retry_error_update_datas:
                # 批量更新失败，开始逐条重试
                total_failed_items = self._count_total_failed_items()
                logger.warning(f"批量更新失败，开始逐条重试，失败数据批次数量: {len(self.retry_error_update_datas)}，实际失败条目数量: {total_failed_items}")
                self._retry_failed_update_items_one_by_one(update_endpoint, internal_side_config, sync_record, operation_type='updated')

        # 执行批量创建
        if create_items:
            logger.info(f"Batch creating {len(create_items)} items for model: {model_id}")
            self._batch_update_internal_items(update_endpoint, internal_side_config, create_items, sync_record=sync_record)
            if self.retry_error_update_datas:
                # 批量创建失败，开始逐条重试
                total_failed_items = self._count_total_failed_items()
                logger.info(f"批量创建失败，开始逐条重试，失败数据批次数量: {len(self.retry_error_update_datas)}，实际失败条目数量: {total_failed_items}")
                self._retry_failed_update_items_one_by_one(update_endpoint, internal_side_config, sync_record)

        # 执行批量删除
        if delete_items:
            logger.info(f"Batch deleting {len(delete_items)} items for model: {model_id}")
            self._batch_delete_internal_items(delete_endpoint, internal_side_config, delete_items, sync_record=sync_record)
            if self.retry_error_delete_datas:
                # 批量删除失败，开始逐条重试
                logger.info(f"批量删除失败，开始逐条重试，失败数据数量: {len(self.retry_error_delete_datas)}")
                self._retry_failed_delete_items_one_by_one(delete_endpoint, internal_side_config, sync_record)

        # 完成统计验证和报告
        self._finalize_sync_record_statistics(sync_record)

        # 清除当前模型ID
        self.current_model_id = None
        logger.info(f"Completed syncing from cloud to internal for model: {model_id}")

    def _create_internal_data_index(self, internal_data, internal_primary_key):
        """创建行内侧数据索引"""
        internal_data_index = {}
        if not internal_data:
            return internal_data_index
        for item in internal_data:
            key = self._get_internal_key(item, internal_primary_key)
            if key:
                internal_data_index[key] = item
        return internal_data_index

    def _process_cloud_data(self, model_id, model_config, assembled_data, internal_data_index,
                            internal_primary_key, field_mappings, resource_type,
                            sync_record=None):
        """处理云内数据并准备更新/创建列表"""
        update_items = []
        create_items = []
        processed_keys = set()

        for assembled_item in assembled_data:
            # 获取主模型数据和关联数据
            cloud_item = assembled_item.get('primary', {})
            related_data = assembled_item.get('related', {})
            source_id = assembled_item.get('_source_id')

            # 更新数据源统计
            if sync_record and source_id:
                sync_record.add_source_statistic(source_id, 'total')

            # 整条记录级别的验证 针对特定的模型 现在模型不做同步
            is_exist = self._has_record_level_checks(model_config)
            if is_exist and not self._validate_item(cloud_item, model_config):  # 加字段作用域，排除
                logger.debug(f"Item {cloud_item} did not pass filter checks, skipping")
                if sync_record:
                    sync_record.add_skipped(1, source_id)
                continue

            # 创建行内侧数据项并应用字段映射
            internal_item = self._create_internal_data(cloud_item, related_data, field_mappings, model_config)

            # 生成行内侧主键
            internal_key = self._get_internal_key(internal_item, internal_primary_key)
            if not internal_key:
                logger.warning(f"Missing internal primary key for item, skipping")
                if sync_record:
                    sync_record.add_skipped(1, source_id)
                continue

            # 标记该键已处理
            processed_keys.add(internal_key)

            # 检查是否存在并决定更新或创建
            existing_item = internal_data_index.get(internal_key)
            if existing_item:
                # 将所有同步的数据缓存
                self.sync_data[model_id][source_id].append(internal_item)

                updated_item, has_changes = self._prepare_sync_item(source_id, internal_key, existing_item, internal_item, sync_record, model_config)
                # updated_item = self._prepare_update_item(source_id, internal_key, existing_item, internal_item, sync_record, model_config)
                if updated_item:
                    update_items.append(updated_item)
                    logger.info(f"Item with key {internal_key} has changes, will be updated")
                    if sync_record and source_id:
                        sync_record.add_source_statistic(source_id, 'updated')
                    if has_changes:
                        self._record_field_changes(internal_key, existing_item, updated_item, sync_record=sync_record)

            elif resource_type == 'cloud':

                # 将所有同步的数据缓存
                self.sync_data[model_id][source_id].append(internal_item)

                # 云资源：云内存在行内不存在，创建
                create_items.append(internal_item)
                logger.info(f"Cloud resource with key {internal_key} not found in internal, will be created")
                # 记录新建项的所有字段
                self._record_field_changes(internal_key, None, internal_item, sync_record=sync_record, operation_type="created")
                if sync_record and source_id:
                    sync_record.add_source_statistic(source_id, 'created')
            else:
                # 物理资源：行内不存在，不处理
                if sync_record:
                    sync_record.add_skipped(1, source_id)
                logger.info(f"Physical resource with key {internal_key} not found in internal, skipping creation")

        return update_items, create_items, processed_keys

    def _record_field_changes(self, record_id, old_item, new_item, field='', sync_record=None, operation_type='updated'):
        """记录整条数据变更，保存具体条目信息
        - field 参数存储旧数据
        - old_value 参数存储新数据
        - new_value 参数存储操作类型
        """
        if not sync_record:
            return

        # 直接调用，参数含义明确
        sync_record.add_change(record_id, field, old_item, new_item, operation_type)


    def _has_record_level_checks(self, model_config):
        """检查模型配置中是否存在记录级别的过滤检查

        Args:
            model_config: 模型配置

        Returns:
            bool: 如果存在作用域为'record'的过滤检查则返回True，否则返回False
        """
        # 获取过滤检查配置
        filter_checks = model_config.get('filter_checks', [])

        # 检查是否有任何检查的作用域是'record'
        for check in filter_checks:
            scope = check.get('scope', 'field')
            if scope == 'record':
                return True

        return False

    def _create_internal_data(self, cloud_item, related_data, field_mappings, model_config):
        """创建行内侧数据项并应用字段映射"""
        internal_item = {}
        excluded_item = {}
        for mapping in field_mappings:
            internal_field = mapping.get('internal_field')
            internal_reference_key = mapping.get('internal_reference_key', '')
            cloud_field = mapping.get('cloud_field')
            transform = mapping.get('transform')
            transform_params = mapping.get('transform_params', {})
            required = mapping.get('required', False)
            if not internal_field:
                continue
            # 获取云内侧值
            value = self._get_cloud_field_value(cloud_item, cloud_field, transform, transform_params, related_data)
            logger.debug(f"云内数据字段：{cloud_field}，行内数据字段：{internal_field}，转换后的值：{value}")
            # 如果cloud_field是custom,则校验翰行内字段，值也是对应转换之后的值
            # 如果cloud_field不是custom,则校验行内字段，值是云内侧的值
            is_check = mapping.get("is_check", False)
            check_field = cloud_field if cloud_field != "custom" else internal_field
            check_value = value if cloud_field == "custom" else None
            if is_check and not self._validate_item(cloud_item, model_config, check_field, temp_value=check_value):
                logger.debug(f"Field {cloud_field} did not pass field-level filter checks, skipping")
                continue

            # 设置字段值 排除空值 弥补没有配置检查项，但值为空的情况
            if required:
                if internal_reference_key:
                    internal_item[internal_field] = {"ClassKey": internal_reference_key, internal_reference_key: value}
                else:
                    internal_item[internal_field] = value
            else:
                excluded_item[internal_field] = value
        logger.debug(f'excluded_item:{excluded_item}')
        return internal_item

    def _get_cloud_field_value(self, cloud_item, cloud_field, transform, transform_params, related_data):
        """获取云内侧字段值并应用转换"""
        if cloud_field == 'custom':
            # 自定义转换，需要使用转换函数
            if transform:
                # 应用转换函数
                try:
                    value = self._apply_transform(transform, cloud_item, related_data, transform_params)
                except Exception as e:
                    logger.error(f"Error applying transform {transform}: {str(e)}")
                    value = None
            else:
                value = None
        else:
            # 直接获取字段值
            value = cloud_item.get(cloud_field)
            # 应用转换
            if transform and transform != 'None':
                logger.debug(f"Applying transform {transform} for field {cloud_field}")
                try:
                    value = self._apply_transform(transform, cloud_item, related_data, transform_params, value=value)
                except Exception as e:
                    logger.error(f"Error applying transform {transform}: {str(e)}")
                    # 保留原始值
                    value = cloud_item.get(cloud_field)

        return value

    # def _prepare_update_item(self, source_id, internal_key, existing_item, internal_item, sync_record=None, model_config=None):
    #     """准备更新项目，检查是否有变更"""
    #     has_changes = False
    #     backfill_field = model_config.get('backfill_field', [])
    #     # updated_item = copy.deepcopy(existing_item)
    #     updated_item = {}
    #     for field, value in internal_item.items():
    #         # 外键数据结构直接加入，行内外键存入和查询的数据统一字段，展示不同，比较无意义
    #         if isinstance(value, dict):
    #             updated_item[field] = value
    #             continue
    #
    #         # 更新的关键参数 classKey
    #         if field in ["InstanceId", "Code", "SerialNumber"]:
    #             updated_item[field] = value
    #             continue
    #
    #         # 关联字段位
    #         old_value = existing_item.get(field)
    #         # 检查值是否变化
    #         if old_value != value:
    #             updated_item[field] = value
    #             has_changes = True
    #             logger.debug(f"Field {field} changed from {old_value} to {value}")
    #             # 统计与记录
    #             if sync_record:
    #                 sync_record.add_change(internal_key, field, old_value, value)
    #
    #     # 字段回填
    #     self._handle_backfill_field(updated_item, backfill_field, existing_item)
    #
    #
    #     if not has_changes and sync_record:
    #         sync_record.add_skipped(1, source_id)
    #
    #     logger.debug(f"has_changes:{has_changes}")
    #     # 只有在有变更时才返回更新项
    #     return updated_item if has_changes else None

    def _handle_backfill_field(self, updated_item, backfill_field, internal_primary_key, internal_key):
        if backfill_field:
            # 获取行内现存数据
            history_data = self.history_data.get(self.current_model_id)
            if history_data and 'internal_data' in history_data:
                existing_items = history_data['internal_data']
            else:
                existing_items = self.internal_data.get(self.current_model_id)

            internal_data_index = self._create_internal_data_index(existing_items, internal_primary_key)
            existing_item = internal_data_index.get(internal_key)

            if not existing_item:
                logger.warning(f"Existing item not found for key {internal_key}, skipping backfill")
                return

            for item in backfill_field:
                field_name = item.get('field_name', '')
                is_reference = item.get('is_reference', False)
                # 程序到这里，已经通过匹配项检查对必填字段不满足条件的做了过滤
                is_mapping = item.get('is_mapping', False)
                class_key = item.get('class_key', '')

                if is_mapping and field_name in updated_item.keys() and updated_item.get('field_name'):
                    continue

                if field_name not in existing_item.keys():
                    logger.warning(
                        f'The required field ({field_name}) for back-filling does not exist in the current data entry.')
                    continue

                if is_reference and class_key:
                    updated_item[field_name] = {'ClassKey': class_key, class_key: existing_item.get(field_name)}
                else:
                    updated_item[field_name] = existing_item.get(field_name)

    def _extract_previous_cloud_keys(self, previous_cloud_data, could_match_key):
        """从历史数据中提取所有云内资源的主键"""
        previous_cloud_keys = set()
        for src_id, src_data in previous_cloud_data.items():
            primary_data = src_data.get('primary', [])
            for item in primary_data:
                # 使用与当前处理相同的逻辑获取内部键
                internal_key = self._get_internal_key(item, could_match_key)
                if internal_key:
                    previous_cloud_keys.add(internal_key)
        return previous_cloud_keys

    def _sync_internal_to_cloud(self, model_id: str, model_config: Dict[str, Any],
                                internal_data: List[Dict[str, Any]], assembled_data: List[Dict[str, Any]],
                                field_mappings: List[Dict[str, Any]], sync_record=None):
        """从行内侧同步到云内侧

        实现需求：
        1. 属性转换规则：原值、枚举转换、API调用获取
        2. 同步原则：
           - 物理类资源：资源在行内和云内都存在才同步
           - 云资源：资源在行内和云内都存在时进行云内到行内同步
        3. 变更检测：与上一个任务周期数据比较，只在有变更时同步
        """

        logger.info(f"Syncing from internal to cloud for model: {model_id}")

        sync_direction = 'internal_to_cloud'
        self.sync_direction = sync_direction

        # 更新方向统计
        if sync_record:
            sync_record.internal_to_cloud_count += len(internal_data)

        # 获取云内侧配置
        cloud_side_config = model_config.get('cloud_side', {})
        cloud_model = cloud_side_config.get('primary_model')
        cloud_primary_key = cloud_side_config.get('primary_key')
        # 更新配置
        if not cloud_model or not cloud_primary_key:
            logger.warning(f"Missing cloud model configuration for {model_id}, skipping")
            return

        # 设置当前模型ID
        self.current_model_id = model_id

        # 获取行内侧配置
        internal_side_config = model_config.get('internal_side', {})
        internal_primary_key = internal_side_config.get('primary_key')

        if not internal_primary_key:
            logger.warning(f"Missing internal primary key for {model_id}, skipping")
            return
        # 处理每一个行内数据之前 需要先查询一下我需要同步的字段是否已经存在,如果不存在，就需要去给这个数据新建一个字段
        self.handle_model_attr(field_mappings, cloud_model, model_id)
        # 获取资源类型（物理类资源 or 云资源）
        resource_type = model_config.get('resource_type', 'cloud')  # 默认为云类资源
        logger.info(f"Resource type for model {model_id}: {resource_type}")

        # 加载上一个任务周期的数据用于变更检测  优化
        previous_data = self._load_previous_sync_data(model_id)

        # 创建云内侧数据索引，按数据源分组
        cloud_data_by_source = {}  # 这里面的值是{"source_1":{"Code1":data}}
        cloud_data_by_code = {}  # 按CODE主键索引云内数据  {"Code1":data,"Code2":data2}
        # 开始遍历云内数据  每次一条数据
        for item in assembled_data:
            source_id = item.get('_source_id')
            if not source_id:
                continue

            if source_id not in cloud_data_by_source:
                cloud_data_by_source[source_id] = {}

            # 按云内主键索引  这里需要用resId 做更新
            cloud_id = item.get('primary', {}).get(cloud_primary_key)
            if cloud_id:
                cloud_data_by_source[source_id][cloud_id] = item

            # 按CODE主键索引（用于根据CODE获取云内ID）
            primary_data = item.get('primary', {})  # 云内数据
            # 根据配置的字段在云内数据中拼接出 行内关键字段的值  比如我在行内是PCServer_445588 那么我就要根据配置的项对数据进行组装 根据配置的信息组装出一个Code值
            code_value = None
            for mapping in field_mappings:
                if (mapping.get('internal_field') == internal_primary_key and
                        mapping.get('sync_direction') in ['internal_to_cloud', 'bidirectional']):
                    cloud_field = mapping.get('cloud_field')
                    transform = mapping.get('transform')
                    transform_params = mapping.get('transform_params')
                    if cloud_field == 'custom':  # 如果是需要通过拼接来的数据 调用transform获取到数据 组装成Key
                        code_value = self._apply_transform(transform, primary_data, {}, transform_params)
                        break
                    else:  # 不需要组装的就直接获取这个字段
                        code_value = primary_data.get(cloud_field)
                        break
            if code_value:
                cloud_data_by_code[code_value] = item

        # 定义一个dict 存放每个数据源需要更新的数据
        update_cloud_model_by_source = {}
        # 定义一个dict 存放每一个数据源需要更新的关系
        update_cloud_relation_by_source = {}

        # 获取同步数量限制
        sync_limits = self._get_sync_limits(model_id, sync_direction)
        update_limit = sync_limits['update']
        create_limit = sync_limits['create']
        delete_limit = sync_limits['delete']

        # 初始化操作计数器
        update_count = 0
        create_count = 0
        delete_count = 0

        # 初始化同步记录计数
        # total_records 已在主同步函数中设置，此处不重复设置
        # if sync_record:
        #     sync_record.total_records += len(internal_data)

        # 处理每个行内侧数据项
        for internal_item in internal_data:
            # 获取行内侧主键值（CODE）
            internal_id = internal_item.get(internal_primary_key)
            if not internal_id:
                logger.warning(f"Missing internal primary key for item, skipping")
                if sync_record:
                    # 使用统一的跳过统计方法
                    sync_record.add_skipped(1, source_id)
                continue

            # 根据CODE获取对应的云内数据项
            cloud_item_data = cloud_data_by_code.get(internal_id)
            # 应用同步原则
            if not cloud_item_data:
                logger.debug(f"Cloud resource {internal_id} not found in cloud, no action needed")
                if sync_record:
                    # 使用统一的跳过统计方法
                    sync_record.add_skipped(1, source_id)
                continue
            source_id = cloud_item_data.get('_source_id')
            # 检查变更：与上一个任务周期的数据进行比较  上一个周期上报的数据
            previous_model_data = previous_data.get('model_data', {}) if previous_data else {}
            if source_id in previous_model_data.keys():
                previous_model_data_list = previous_model_data.get(source_id)
                previous_item = next((item for item in previous_model_data_list if
                                      item["resId"] == cloud_item_data.get('primary').get('resId')), None)
                has_changes = self._detect_changes(internal_item, previous_item, field_mappings)
                if not has_changes:
                    logger.debug(f"No changes detected for item {internal_id}, skipping")
                    if sync_record:
                        # 使用统一的跳过统计方法
                        sync_record.add_skipped(1, source_id)
                    continue

            # 检查是否达到更新限制
            if update_limit > 0 and update_count >= update_limit:
                logger.info(f"Reached update limit of {update_limit} for model {model_id}, skipping")
                continue

            # 应用字段映射，装需要更新的云内测的数据
            cloud_item = {"resId": cloud_item_data.get('primary').get('resId')}  # 更新云内数据必须属性
            # 定义一个dict 将所有的需要更新的关系的数据放在一起 这里放的数据格式{relation_model": [{reation1},{relation2}]}
            updata_relation_model_dict = {}
            for mapping in field_mappings:
                # 只处理从行内到云内的字段映射
                sync_direction = mapping.get('sync_direction', '')
                if sync_direction not in ['internal_to_cloud', 'bidirectional']:
                    continue

                internal_field = mapping.get('internal_field')
                cloud_field = mapping.get('cloud_field')
                transform = mapping.get('transform')
                transform_params = mapping.get('transform_params', {})
                relation_model = transform_params.get('relation_model')
                if not internal_field or not cloud_field:
                    continue

                # 获取行内侧字段值
                internal_value = internal_item.get(internal_field)

                # 处理关联关系更新
                if transform == "compare_relation_field":
                    # 获取关联数据
                    cloud_value = self._apply_transform(
                        "compare_relation_field", internal_item, cloud_item_data.get('related'),
                        transform_params, internal_value
                    )
                    if not cloud_value:
                        continue
                    update_items = {}
                    # 更新或添加关联数据
                    if not updata_relation_model_dict:
                        update_items = {'id': cloud_value.get('id'), cloud_field: internal_item.get(internal_field)}
                    else:
                        relation_data = updata_relation_model_dict.get(relation_model, [])
                        existing_item = next((item for item in relation_data if item["id"] == cloud_value.get('id')),
                                             None
                                             )
                        if existing_item:
                            existing_item[cloud_field] = internal_item.get(internal_field)
                            update_items = existing_item
                        else:
                            update_items[cloud_field] = internal_item.get(internal_field)
                    if update_items:
                        # 将关联关系数据添加到cloud_item中
                        relation_model = transform_params.get('relation_model')
                        if relation_model not in updata_relation_model_dict:
                            updata_relation_model_dict[relation_model] = []
                        # 在这里判断一直这条数据是否已经存在  存在就不用存 不存在才存
                        if not any(item["id"] == cloud_value.get('id') for item in
                                   updata_relation_model_dict[relation_model]):
                            updata_relation_model_dict[relation_model].append(update_items)
                    continue
                # 应用转换函数
                cloud_value = self._apply_transform(transform, internal_item, {}, transform_params,
                                                    internal_value) if transform else internal_value

                # 设置云内侧字段值
                if cloud_value is not None:
                    cloud_item[cloud_field] = str(cloud_value)
            # cloud_item 存放的是本次需要更新的模型数据  updata_relation_model_dict 存放本条数据所关联的 关联关系数据

            # 根据这条数据的item.get('_source_id')判断这个数据应该通过那个数据源上报更新
            if source_id not in update_cloud_model_by_source:
                update_cloud_model_by_source[source_id] = []
                update_cloud_relation_by_source[source_id] = []
            update_cloud_model_by_source[source_id].append(cloud_item)  # 根据数据源不同将模型数据存放在不同的数据源dict 里面
            if updata_relation_model_dict:
                update_cloud_relation_by_source[source_id].append(updata_relation_model_dict)

            # 增加更新计数
            update_count += 1

        # 获取更新端点
        update_endpoint_key = f"update_{cloud_model}"
        self._get_endpoint_url(update_endpoint_key, cloud_model)
        total_updated = 0
        # 这里是去更新云内数据模型
        if update_cloud_model_by_source:
            for source_id, update_models in update_cloud_model_by_source.items():
                success = self._update_single_source_item(update_endpoint_key, update_models, source_id)
                if success:
                    logger.info(
                        f"Successfully updated {len(update_models)} cloud items for source {source_id}")
                    if sync_record:
                        # 使用统一的成功统计方法
                        sync_record.add_success(len(update_models), 'updated', source_id)
                        total_updated += len(update_models)
                else:
                    logger.error(f"Failed to update some cloud items for source {source_id}")
                    if sync_record:
                        # 使用统一的失败统计方法
                        sync_record.add_failed(len(update_models), source_id)
        update_internal_to_cloud_data = {"model_data": update_cloud_model_by_source}
        if update_cloud_relation_by_source:
            aggregate_relation_data_dict = self.aggregate_relation_data(update_cloud_relation_by_source)
            update_internal_to_cloud_data["relation_data"] = aggregate_relation_data_dict
            for source_id, update_relations in aggregate_relation_data_dict.items():
                for item_key, item_value in update_relations.items():
                    update_relation_key = f"update_relation_{item_key}"
                    # if update_relation_key not in self.endpoints.get("cloud_side"):
                    update_relations_endpoint = self.endpoints.get("cloud_side").get('cloud_relation_api',
                                                                                     "").replace(
                        "${relationName}", item_key)
                    self.endpoints.get("cloud_side")[update_relation_key] = update_relations_endpoint
                    success = self._update_single_source_item(update_relation_key, item_value, source_id)
                    if success:
                        logger.info(
                            f"Successfully updated {len(item_value)} cloud items for source {source_id}")
                    else:
                        logger.error(f"Failed to update some cloud items for source {source_id}")

        # 保存当前任务周期的数据，用于下次变更检测
        update_internal_to_cloud_data = {"model_data": update_cloud_model_by_source,
                                         "relation_data": update_cloud_relation_by_source}
        self._save_current_sync_data(model_id, update_internal_to_cloud_data)
        logger.info(f"Completed syncing from internal to cloud for model: {model_id}")

    def handle_model_attr(self, field_mappings, cloud_model, current_model_id):
        """
            对云内模型进行属性新增 如果需要从行内同步到云内,且字段不存在，就新增字段

            参数： field_mappings 配置的需要同步的属性字段
                  cloud_model 云内模型
                  current_model_id 当前模版ID
        """
        # 筛选需要同步到云的字段
        sync_to_cloud_fields = [
            mapping['cloud_field']
            for mapping in field_mappings
            if mapping.get('sync_direction') in ['internal_to_cloud', 'bidirectional']
               and mapping.get('cloud_field') != 'custom'
        ]
        # 确保端点配置存在
        cloud_endpoints = self.endpoints.get("cloud_side", {})
        query_attr_key = f"query_attrs_{cloud_model}"
        insert_attr_key = f"insert_attrs_{cloud_model}"
        # 查询属性接口
        if query_attr_key not in cloud_endpoints:
            query_endpoint = cloud_endpoints.get('query_attrs', '').replace("{className}", cloud_model)
            cloud_endpoints[query_attr_key] = query_endpoint
        # 新增属性接口
        if insert_attr_key not in cloud_endpoints:
            insert_endpoint = cloud_endpoints.get('update_attrs', '').replace("{className}", cloud_model)
            cloud_endpoints[insert_attr_key] = insert_endpoint
        # 先看这个模型配置的是几个数据源 每个数据源都要去检查数据源内的模型是否已经有了需要更新的属性
        model_sources = self._get_model_sources(current_model_id)
        pattern = re.compile(r'^[a-zA-Z_][a-zA-Z0-9_]{0,29}$')
        valid_types = {"int", "string", "float", "boolean", "long", "double"}
        for model_source_item in model_sources:
            client_key = f"cloud_{model_source_item}"
            response = self._call_cloud_api(query_attr_key, {}, method='GET', client_key=client_key)
            existing_attrs = {item["name"] for item in self._safe_get_response_field(response, 'attrs', [])}
            # 找出缺失的字段
            missing_fields = [field for field in sync_to_cloud_fields if field not in existing_attrs]
            if not missing_fields:
                continue
            for missing_field in missing_fields:
                if not pattern.match(missing_field):
                    logger.info(
                        "Missing field do not meet the requirements,只能包含数字、字母、下划线，且只能以字母或下划线开头，最大长度30")
                    continue
                field_mapping = next((item for item in field_mappings if item.get("cloud_field") == missing_field),
                                     None)
                add_attr_params = field_mapping.get('add_attr_params', {})
                if not add_attr_params:
                    logger.info("Missing field do not have attr params, can not add attrs")
                    continue
                if add_attr_params.get('type') not in valid_types:
                    logger.info(
                        "Missing field type do not meet the requirements,only int、string、float、boolean、long、double")
                    continue
                add_attr_param = {
                    "name": missing_field,
                    "writeable": True,
                    "unique": False,
                    "mandatory": True,
                    "type": add_attr_params.get('type'),
                    "length": add_attr_params.get('length', 0),
                    "displayName": {"en_US": add_attr_params.get('display_name_zh'),
                                    "zh_CN": add_attr_params.get('display_name_cn')},
                    "defaultValue": add_attr_params.get('defaultValue', None),
                    "enumInfo": add_attr_params.get('enumInfo', None),
                    "unit": add_attr_params.get('unit', None),
                    "analysisType": add_attr_params.get('analysisType', None)
                }
                logger.info(f"add source:{model_source_item},model:{cloud_model},attrs params:{add_attr_param}")
                response = self._call_cloud_api(insert_attr_key, {}, method='PUT', data=add_attr_param,
                                                client_key=client_key)
                if self._safe_get_response_field(response, "status") == "success":
                    logger.info(f"add attr {missing_field} success")
                else:
                    logger.info(f"add attr {missing_field} faild")

    def aggregate_relation_data(self, data: Dict) -> Dict:
        """
        对关系数据进行聚合处理，将相同键的数据合并为列表

        参数:
            data (Dict[str, List[Dict[str, List[Any]]]]):
                输入数据字典，结构为 {数据源键: [记录1, 记录2,...]}，
                每个记录为 {关系类型: 关系数据列表}

        返回:
            Dict[str, Dict[str, List[Any]]]:
                聚合后的数据，结构为 {数据源键: {关系类型: 合并后的关系数据列表}}
        """
        aggregated_data: Dict[str, Dict[str, List[Any]]] = {}

        # 遍历每个数据源及其记录
        for source_key, source_records in data.items():
            source_aggregated: Dict[str, List[Any]] = {}

            # 遍历当前数据源的所有记录
            for record in source_records:
                # 处理单条记录（假设每条记录只有一个关系类型键）
                relation_type, relation_items = next(iter(record.items()), (None, []))
                if relation_type is None:
                    continue  # 跳过空记录

                # 聚合相同关系类型的数据
                source_aggregated.setdefault(relation_type, []).extend(relation_items)

            # 仅当有有效聚合数据时才添加到结果
            if source_aggregated:
                aggregated_data[source_key] = source_aggregated

        return aggregated_data

    def _load_previous_sync_data(self, model_id: str) -> Dict[str, Any]:
        """加载上一个任务周期的同步数据"""
        cache_file = os.path.join(self.cache_dir, f"{model_id}_previous_sync.json")
        try:
            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    logger.debug(f"Loaded previous sync data for {model_id}: {len(data)} items")
                    return data
        except Exception as e:
            logger.warning(f"Failed to load previous sync data for {model_id}: {e}")
        return {}

    def _save_current_sync_data(self, model_id: str, current_data: Dict[str, Any]):
        """保存当前任务周期的同步数据"""
        cache_file = os.path.join(self.cache_dir, f"{model_id}_previous_sync.json")
        try:
            # # 转换为以主键为键的字典格式
            # data_dict = {}
            # for item in current_data:
            #     primary_key = self.models.get(model_id, {}).get('internal_side', {}).get('primary_key', 'Code')
            #     key = item.get(primary_key)
            #     if key:
            #         data_dict[key] = item

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(current_data, f, ensure_ascii=False, indent=2)
                logger.debug(f"Saved current sync data for {model_id}: {len(current_data)} items")
        except Exception as e:
            logger.warning(f"Failed to save current sync data for {model_id}: {e}")

    def _detect_changes(self, current_item: Dict[str, Any], previous_item: Dict[str, Any],
                        field_mappings: List[Dict[str, Any]]) -> bool:
        """检测数据是否有变更"""
        if not previous_item:
            # 如果没有历史数据，认为有变更
            return True

        # 检查需要同步的字段是否有变更
        for mapping in field_mappings:
            sync_direction = mapping.get('sync_direction', '')
            if sync_direction not in ['internal_to_cloud', 'bidirectional']:
                continue

            field_name = mapping.get('internal_field')
            if not field_name:
                continue

            current_value = current_item.get(field_name)
            previous_value = previous_item.get(field_name)

            if current_value != previous_value:
                logger.debug(f"Field {field_name} changed: {previous_value} -> {current_value}")
                return True

        return False

    def _sync_bidirectional(self, model_id: str, model_config: Dict[str, Any],
                            cloud_data: Dict[str, Any], internal_data: List[Dict[str, Any]],
                            field_mappings: List[Dict[str, Any]]):
        """双向同步"""
        # 实现双向同步逻辑
        pass

    def _determine_model_order(self) -> List[str]:
        """确定模型同步顺序

        根据模型配置中的 sync_model_order 字段确定同步优先级：
        - 数字越小优先级越高
        - 没有配置 sync_model_order 字段的模型跳过同步

        Returns:
            List[str]: 按优先级排序的模型ID列表
        """
        models_with_order = []

        # 遍历所有模型，收集有 sync_model_order 配置的模型
        for model_id, model_config in self.models.items():
            if not isinstance(model_config, dict):
                continue

            sync_order = model_config.get('sync_model_order')
            if sync_order is not None:
                try:
                    # 确保 sync_order 是数字
                    order_value = int(sync_order)
                    models_with_order.append((model_id, order_value))
                    logger.debug(f"Model {model_id} has sync_model_order: {order_value}")
                except (ValueError, TypeError):
                    logger.warning(f"Model {model_id} has invalid sync_model_order value: {sync_order}, skipping")
            else:
                logger.debug(f"Model {model_id} has no sync_model_order configured, skipping sync")

        # 按 sync_model_order 值排序（数字越小优先级越高）
        models_with_order.sort(key=lambda x: x[1])

        # 提取排序后的模型ID列表
        model_order = [model_id for model_id, _ in models_with_order]

        logger.info(f"Determined model sync order: {model_order}")
        if models_with_order:
            logger.info("Model sync priorities:")
            for model_id, order_value in models_with_order:
                logger.info(f"  {model_id}: priority {order_value}")

        return model_order

    def _fetch_cloud_data(self, endpoint: str, model_config: Dict[str, Any], source_id: str = None) -> List[
        Dict[str, Any]]:
        """获取云内侧数据

        这个方法是对_fetch_cloud_data_with_params的简单封装，不传入额外参数

        Args:
            endpoint: 端点名称
            model_config: 模型配置
            source_id: 数据源ID，如果提供则使用指定的数据源

        Returns:
            获取的数据列表
        """
        # 直接调用_fetch_cloud_data_with_params，不传入额外参数
        return self._fetch_cloud_data_with_params(endpoint, model_config, {}, source_id)


    def _fetch_internal_data(self, endpoint: str, model_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取行内侧数据，支持主模型和关联模型的分页查询"""
        # 1. 提取公共配置
        internal_side_config = model_config.get('internal_side', {})
        model_id = model_config.get('model_id')
        batch_size = self._get_batch_size(internal_side_config)
        api_config = internal_side_config.get('get_params', {}).get('api_config', {})
        pagination_cfg = api_config.get('pagination', {})
        result_path = api_config.get('result_path', 'data')

        # 2. 主模型数据获取
        main_params = self._build_main_params(internal_side_config)
        all_data = self._fetch_paginated_data(
            endpoint=endpoint,
            base_params=main_params,
            result_path=result_path,
            pagination_cfg=pagination_cfg,
            batch_size=batch_size
        )
        self.get_internal_related_data(internal_side_config,main_params,endpoint,result_path,pagination_cfg,batch_size, model_id)


        return all_data

    def get_internal_related_data(self, internal_side_config, main_params,endpoint,
                                  result_path, pagination_cfg, batch_size,model_id):
        # 3. 关联模型数据处理
        if related_models := internal_side_config.get("related_models"):
            self.internal_related_data = {}  # 重置关联数据存储
            for model in related_models:
                related_params = self._build_related_params(model, main_params)
                model_data = self._fetch_paginated_data(
                    endpoint=endpoint,
                    base_params=related_params,
                    result_path=result_path,
                    pagination_cfg=pagination_cfg,
                    batch_size=batch_size
                )
                if model_data:
                    self.internal_related_data.setdefault(model_id, {})[model.get("className")] = model_data
        # if not self.internal_application_system_data:
        #     self.get_internal_application_system_data()
        application_system_related = self.merge_application_data()
        if application_system_related:
            self.internal_related_data.setdefault(model_id, {})["ApplicationSystem"] = application_system_related
        if model_id in self.internal_related_data:
            # 把数据保存到缓存
            self._save_to_cache(f"internal_related_{model_id}", self.internal_related_data[model_id])

    def get_internal_application_system_data(self):
        """"全量查询行内的应用系统数据"""
        internal_side_config = self.endpoints.get("internal_side",{})
        public_model_config = internal_side_config.get('publicModel', {})
        pagination_cfg = public_model_config.get("pagination",{})
        batch_size = self._get_batch_size(public_model_config)
        main_params = {"isRelatedCIId":True}
        related_params = self._build_related_params(public_model_config, main_params)
        model_data = self._fetch_paginated_data(
            endpoint="get_ci",
            base_params=related_params,
            result_path="data",
            pagination_cfg=pagination_cfg,
            batch_size=batch_size
        )
        if model_data:
            self.internal_application_system_data.extend(model_data)

    def  merge_application_data(self):
        default_dict = {}
        if self.internal_application_system_data:
            mo_application_info = next((item for item in self.internal_application_system_data if item.get("SysNo") == "L00500"), None)
            # 给一个默认值 这里直接用默认新云系统的SysNo作为key  配置改动最小
            default_dict["L00500"] = mo_application_info
        # 先在行内应用系统里面找到一个新云应用系统Code对应的数据  对应了应用系统的责任人信息、机构信息等
        # 构建系统编号到数据的映射 (使用字典推导式)
        sysno_map = {item['SysNo']: item for item in self.internal_application_system_data if 'SysNo' in item}
        if self.current_model_id in ['CLOUD_CCE_NAMESPACE', 'CLOUD_CCE_CONTAINER', 'CLOUD_CCE_SERVICE', 'CloudDaemonset', 'CloudDeployment', 'CloudStatefulset']:
            main_data = self.merge_special_application_code(sysno_map)
        else:
            # 生成主数据字典
            main_data = {
                app_key: sysno_map[app_value]
                for app_key, app_value in self.application_code.items()
                if app_value in sysno_map
            }
        # 合并主数据和默认值字典后返回
        return {**main_data, **default_dict}

    def merge_special_application_code(self, sysno_map: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并特殊应用代码

        Args:
            sysno_map: 系统编号映射表

        Returns:
            合并后的主数据字典
        """
        main_data: Dict[str, Any] = {}
        # 获取当前模型ID对应的数据，默认空字典
        current_model_data: Dict[str, Any] = self.cloud_data.get(self.current_model_id, {})

        if not current_model_data:
            return main_data

        # 常量定义
        PRIMARY_KEY = "primary"
        NAME_KEY = "name" if self.current_model_id == 'CLOUD_CCE_NAMESPACE' else 'namespace'

        for source_value in current_model_data.values():
            # 获取primary数据，默认空列表
            primary_data: List[Dict[str, Any]] = source_value.get(PRIMARY_KEY, [])

            for cloud_data_info in primary_data:
                # 获取名称并转为大写
                name: Optional[str] = cloud_data_info.get(NAME_KEY)
                # 处理None值情况
                if name is None:
                    continue

                # 处理空字符串或仅含空白字符的情况
                name_stripped = name.strip()
                if not name_stripped:
                    continue

                # 取name的前六位（不足六位则取全部），然后转为大写
                name_truncated = name_stripped[:6]  # 截取前六位
                name_upper = name_truncated.upper()
                # 检查是否在映射表中，如果在则添加到结果
                if name_upper in sysno_map:
                    main_data[name_upper] = sysno_map[name_upper]

        return main_data


    # ------------ 辅助方法 ------------
    def _get_batch_size(self, config: Dict) -> int:
        """获取安全的批次大小 (100~10000)"""
        default_size = 100
        if max_size := config.get('maxBatchSize'):
            return min(max(max_size, 1), 10000)  # 确保在1-10000范围内
        return default_size

    def _build_main_params(self, config: Dict) -> Dict:
        """构建主模型请求参数"""
        params = config.get('get_params', {}).copy()
        # 清理空参数并标准化itemNames
        if 'attribute' in params and not params['attribute']:
            del params['attribute']
        if item_names := params.get('itemNames'):
            params['itemNames'] = ','.join(item_names) if isinstance(item_names, list) else item_names
        params['isRelatedCIId'] = params.get('isRelatedCIId', True)
        return params

    def _build_related_params(self, model_cfg: Dict, main_params: Dict) -> Dict:
        """构建关联模型请求参数"""
        related_query_params = {
            "className": model_cfg["className"],
            "itemNames": ','.join(model_cfg.get("itemNames", [])),
            "isRelatedCIId": main_params.get('isRelatedCIId', True)
        }
        if "attribute" in model_cfg and model_cfg["attribute"]:
            related_query_params["attribute"] = model_cfg.get("attribute")

        return related_query_params

    def _fetch_paginated_data(
            self,
            endpoint: str,
            base_params: Dict,
            result_path: str,
            pagination_cfg: Dict,
            batch_size: int
    ) -> List[Dict]:
        """通用分页数据获取方法"""
        page_field = pagination_cfg.get('page_field', 'pageNum')
        size_field = pagination_cfg.get('size_field', 'pageSize')
        page_num, all_data = 1, []

        while True:
            params = {
                **base_params,
                page_field: str(page_num),
                size_field: str(batch_size)
            }
            logger.debug(f"Fetching internal data with params: {json.dumps(params, ensure_ascii=False)}")

            response = self._call_internal_api(endpoint, {}, method="POST", data=params)

            # 使用统一的响应处理方法
            is_success, error_message = self._handle_api_response(response, "获取内部数据", log_success=False)

            if not is_success:
                break

            # 提取数据
            data = self._extract_by_path(response, result_path)
            if data is None:
                logger.warning(f"无法从响应中提取数据，路径: {result_path}")
                break

            all_data.extend(data if isinstance(data, list) else [data])
            if not data or len(data) < batch_size:
                break

            page_num += 1

        return all_data

    def _call_cloud_api(self, endpoint: str, params: Dict[str, Any], method: str = 'GET',
                        data=None, client_key: str = None, retry_count: int = 0) -> Optional[Dict[str, Any]]:
        """调用云内侧API

        Args:
            endpoint: 端点名称
            params: URL参数
            method: HTTP方法，默认为GET
            data: 请求体数据，用于POST/PUT等方法
            client_key: API客户端键名，如果不提供则使用默认的cloud_side
            retry_count: 当前重试次数

        Returns:
            API响应数据，失败时返回None
        """
        # 如果没有提供客户端键名，使用第一个云内客户端
        if not client_key:
            cloud_clients = [k for k in self.api_clients.keys() if k.startswith('cloud_')]
            if not cloud_clients:
                logger.error("No cloud API client available")
                return None
            client_key = cloud_clients[0]
            logger.info(f"No client key provided, using first available client: {client_key}")

        api_client = self.api_clients.get(client_key)
        if not api_client:
            logger.error(f"API client not found for {client_key}")
            return None

        # 获取API配置
        api_config = api_client.get('config', {})
        base_url = api_config.get('base_url', '')

        # 获取重试配置
        max_retries = self.base_config.get('global', {}).get('max_retries', 3)
        retry_interval = self.base_config.get('global', {}).get('retry_interval', 5)

        # 获取超时配置
        timeout = api_config.get('timeout', 30)

        # 获取端点URL
        endpoint_url = self.endpoints.get('cloud_side', {}).get(endpoint)
        if not endpoint_url:
            logger.error(f"Endpoint {endpoint} not found for cloud_side")
            return None

        # 确保有有效的token
        self._ensure_authentication(client_key)

        # 处理请求参数
        processed_params = self._prepare_request_params(params)

        # 发送请求
        try:
            session = api_client.get('session')
            url = f"{base_url}{endpoint_url}"
            session.verify = False

            # 根据请求方法发送请求
            if data:
                logger.debug(f"Calling cloud API {endpoint} with request params: {json.dumps(data, ensure_ascii=False)[:500]}...")
            else:
                logger.debug(f"Calling cloud API {endpoint} with request params: {json.dumps(params, ensure_ascii=False)}")

            start = time.time()

            if method.upper() == 'GET':
                response = session.get(url, params=processed_params, timeout=timeout)
            elif method.upper() == 'POST':
                response = session.post(url, json=processed_params if data is None else data, timeout=timeout)
            elif method.upper() == 'PUT':
                response = session.put(url, json=data, timeout=timeout)
            elif method.upper() == 'DELETE':
                response = session.delete(url, params=processed_params, timeout=timeout)
            else:
                logger.error(f"Unsupported HTTP method: {method}")
                return None

            end = time.time() - start
            logger.info(f"云端API调用耗时: {end:.2f}s, 状态码: {response.status_code}")

            # 检查响应状态
            response.raise_for_status()

            # 解析响应
            result = response.json()
            return result

        except requests.exceptions.Timeout as e:
            logger.warning(f"云端API请求超时 (尝试 {retry_count + 1}/{max_retries + 1}): {e}")
            if retry_count < max_retries:
                logger.info(f"等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
                return self._call_cloud_api(endpoint, params, method, data, client_key, retry_count + 1)
            else:
                logger.error(f"云端API请求超时，已达到最大重试次数: {e}")
                return None

        except requests.exceptions.HTTPError as e:
            status_code = e.response.status_code if e.response else None
            logger.warning(f"云端HTTP错误 {status_code} (尝试 {retry_count + 1}/{max_retries + 1}): {e}")

            # 对于5xx错误进行重试，4xx错误通常不需要重试
            if status_code and 500 <= status_code < 600 and retry_count < max_retries:
                logger.info(f"服务器错误，等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
                return self._call_cloud_api(endpoint, params, method, data, client_key, retry_count + 1)
            else:
                logger.error(f"云端HTTP错误，不再重试: {e}")
                return None

        except requests.exceptions.ConnectionError as e:
            logger.warning(f"云端连接错误 (尝试 {retry_count + 1}/{max_retries + 1}): {e}")
            if retry_count < max_retries:
                logger.info(f"连接失败，等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
                return self._call_cloud_api(endpoint, params, method, data, client_key, retry_count + 1)
            else:
                logger.error(f"云端连接错误，已达到最大重试次数: {e}")
                return None

        except requests.exceptions.RequestException as e:
            # 这里捕获其他未明确处理的requests异常
            logger.warning(f"云端其他请求异常 (尝试 {retry_count + 1}/{max_retries + 1}): {e}")
            if retry_count < max_retries:
                logger.info(f"请求异常，等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
                return self._call_cloud_api(endpoint, params, method, data, client_key, retry_count + 1)
            else:
                logger.error(f"云端请求异常，已达到最大重试次数: {e}")
                traceback.print_exc()
                return None

        except ValueError as e:
            # JSON解析错误通常不需要重试
            logger.error(f"解析云端API响应失败: {e}")
            traceback.print_exc()
            return None

        except Exception as e:
            logger.error(f"调用云端API {endpoint} 发生未知错误: {str(e)}")
            traceback.print_exc()
            return None

    def _call_internal_api(self, endpoint: str, params: Dict[str, Any], method: str = 'POST',
                           data=None, retry_count: int = 0) -> Optional[Dict[str, Any]]:
        """调用行内侧API

        Args:
            endpoint: 端点名称
            params: URL参数
            method: HTTP方法，默认为GET
            data: 请求体数据，用于POST/PUT等方法
            retry_count: 当前重试次数

        Returns:
            API响应数据，失败时返回None
        """
        api_name = 'internal_side'
        api_client = self.api_clients.get(api_name)
        if not api_client:
            logger.error(f"API client not found for {api_name}")
            return None

        # 获取API配置
        api_config = api_client.get('config', {})
        base_url = api_config.get('base_url', '')

        # 获取重试配置
        max_retries = self.base_config.get('global', {}).get('max_retries', 3)
        retry_interval = self.base_config.get('global', {}).get('retry_interval', 5)

        # 获取超时配置，支持不同方法的不同超时时间
        timeout_config = api_config.get('timeout', 30)
        if isinstance(timeout_config, dict):
            timeout = timeout_config.get(method.lower(), 30)
        else:
            timeout = timeout_config
            # 对于POST请求，如果数据量大，使用更长的超时时间
            if method.upper() == 'POST' and data and len(str(data)) > 10000:
                timeout = max(timeout, 1800)  # 至少30分钟

        # 获取端点URL
        endpoint_url = self.endpoints.get(api_name, {}).get(endpoint)
        if not endpoint_url:
            logger.error(f"Endpoint {endpoint} not found for {api_name}")
            return None

        # 构建完整URL
        url = f"{base_url}{endpoint_url}"

        # 确保认证有效
        if not self._ensure_authentication(api_name):
            logger.error(f"Authentication failed for {api_name}")
            return None

        if data:
            logger.debug(f"Calling internal API {endpoint} with request params: {json.dumps(data, ensure_ascii=False)[:500]}...")
        else:
            logger.debug(f"Calling internal API {endpoint} with request params: {json.dumps(params, ensure_ascii=False)}")

        # 发送请求
        try:
            session = api_client.get('session')
            start = time.time()
            session.verify = False
            if method.upper() == 'GET':
                response = session.get(url, params=json.dumps(params), timeout=timeout)
            elif method.upper() == 'POST':
                response = session.post(url, json=data, timeout=timeout)
            elif method.upper() == 'PUT':
                response = session.put(url, params=params, json=data, timeout=timeout)
            elif method.upper() == 'DELETE':
                response = session.delete(url, params=params, timeout=timeout)
            else:
                logger.error(f"Unsupported HTTP method: {method}")
                return None

            end = time.time() - start
            logger.info(f"API调用耗时: {end:.2f}s, 状态码: {response.status_code}")

            # 检查响应状态
            response.raise_for_status()

            # 解析响应
            return response.json()

        except requests.exceptions.Timeout as e:
            logger.warning(f"API请求超时 (尝试 {retry_count + 1}/{max_retries + 1}): {e}")
            if retry_count < max_retries:
                logger.info(f"等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
                return self._call_internal_api(endpoint, params, method, data, retry_count + 1)
            else:
                logger.error(f"API请求超时，已达到最大重试次数: {e}")
                return None

        except requests.exceptions.HTTPError as e:
            # 多种方式尝试获取状态码
            status_code = None
            if hasattr(e, 'response') and e.response is not None:
                status_code = getattr(e.response, 'status_code', None)

            # 如果还是获取不到，尝试从异常消息中解析
            if status_code is None:
                import re
                error_msg = str(e)
                # 匹配类似 "500 Server Error" 的模式
                match = re.search(r'(\d{3})\s+\w+\s+Error', error_msg)
                if match:
                    try:
                        status_code = int(match.group(1))
                        logger.debug(f"从错误消息中解析出状态码: {status_code}")
                    except ValueError:
                        pass

            logger.warning(f"HTTP错误 {status_code} (尝试 {retry_count + 1}/{max_retries + 1}): {e}")

            # 对于5xx错误进行重试，4xx错误通常不需要重试
            # 如果无法确定状态码，也进行重试（保守策略）
            should_retry = False
            if status_code is None:
                # 无法确定状态码，检查错误消息是否包含5xx相关信息
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in ['server error', 'internal error', '5', '4']):
                    should_retry = True
                    logger.info("无法确定状态码，但错误消息显示可能是服务器错误，将进行重试")
            elif 400 <= status_code < 600:
                should_retry = True

            if should_retry and retry_count < max_retries:
                logger.info(f"服务器错误，等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
                return self._call_internal_api(endpoint, params, method, data, retry_count + 1)
            else:
                logger.error(f"HTTP错误，不再重试: {e}")
                return None

        except requests.exceptions.ConnectionError as e:
            logger.warning(f"连接错误 (尝试 {retry_count + 1}/{max_retries + 1}): {e}")
            if retry_count < max_retries:
                logger.info(f"连接失败，等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
                return self._call_internal_api(endpoint, params, method, data, retry_count + 1)
            else:
                logger.error(f"连接错误，已达到最大重试次数: {e}")
                return None

        except requests.exceptions.RequestException as e:
            # 这里捕获其他未明确处理的requests异常
            logger.warning(f"其他请求异常 (尝试 {retry_count + 1}/{max_retries + 1}): {e}")
            if retry_count < max_retries:
                logger.info(f"请求异常，等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
                return self._call_internal_api(endpoint, params, method, data, retry_count + 1)
            else:
                logger.error(f"请求异常，已达到最大重试次数: {e}")
                traceback.print_exc()
                return None

        except ValueError as e:
            # JSON解析错误通常不需要重试
            logger.error(f"解析API响应失败: {e}")
            return None

    def _update_internal_item(self, endpoint: str, internal_side_config: Dict[str, Any], item: Dict[str, Any]) -> bool:
        """更新行内侧项目"""
        # 准备请求数据
        request_data = self._prepare_internal_update_data(internal_side_config, item)

        # 调用API
        response = self._call_internal_api(endpoint, {}, method='POST', data=request_data)

        # 使用统一的响应处理方法
        is_success, message = self._handle_api_response(response, "更新内部项目")
        return is_success

    def _create_internal_item(self, endpoint: str, internal_side_config: Dict[str, Any], item: Dict[str, Any]) -> bool:
        """创建行内侧项目"""
        # 创建和更新使用相同的API
        return self._update_internal_item(endpoint, internal_side_config, item)

    def _ensure_authentication(self, api_name: str) -> bool:
        """确保API认证有效"""
        api_client = self.api_clients.get(api_name)
        if not api_client:
            return False

        # 获取认证配置
        api_config = api_client.get('config', {})
        auth_config = api_config.get('auth', {})
        auth_type = auth_config.get('type')

        # 检查是否需要认证
        if not auth_type:
            return True
        # 检查Token是否有效
        current_time = time.time()
        if api_client.get('token') and api_client.get('token_expires', 0) > current_time:
            return True

        # 根据认证类型进行认证
        if auth_type == 'token':
            return self._authenticate_token(api_name)
        elif auth_type == 'Bearer':
            return self._authenticate_basic(api_name)
        elif auth_type == "sc_token":
            return self._authenticate_sc_token(api_name)
        else:
            logger.error(f"Unsupported authentication type: {auth_type}")
            return False

    def _authenticate_sc_token(self, api_name: str) -> bool:
        return self._perform_authentication(
            api_name,
            "sc_token",
            "X-Subject-Token",
            "X-Auth-Token",
            requests.post
        )

    def _authenticate_token(self, api_name: str) -> bool:
        return self._perform_authentication(
            api_name,
            "password",
            "accessSession",
            "X-Auth-Token",
            requests.put
        )

    def _authenticate_basic(self, api_name: str) -> bool:
        return self._perform_authentication(
            api_name,
            "Bearer",
            "id_token",
            "Authorization",
            requests.post
        )

    def _perform_authentication(self, api_name: str, auth_type: str, token_key: str, header_key: str,
                                request_method) -> bool:
        """执行认证操作的通用方法"""
        api_client = self.api_clients.get(api_name)
        if not api_client:
            logger.error(f"No API client found for {api_name}")
            return False
        api_config = api_client.get('config', {})
        auth_config = api_config.get('auth', {})

        # 获取认证参数
        token_url = auth_config.get('token_url')
        username = auth_config.get('username', '')
        password = CipherUtil.decrypt(auth_config.get('password', ''))
        token_expiry = auth_config.get('token_expiry', 3600)

        logger.debug(
            f"{auth_type.capitalize()} authentication for {api_name}: url={token_url}, username={username}")

        if not token_url:
            logger.error(f"Token URL not specified for {api_name}")
            return False

        # 构建认证URL
        base_url = api_config.get('base_url', '')
        url = f"{base_url}{token_url}"

        # 根据认证类型构建请求参数
        if auth_type == "password":
            param_data = {
                "grantType": "password",
                "userName": username,
                "value": password
            }
        elif auth_type == "sc_token":
            domain_name = auth_config.get('domain_name', '')
            param_data = {
                "auth": {
                    "identity": {
                        "methods": ["password"],
                        "password": {
                            "user": {
                                "name": username,
                                "password": password,
                                "domain": {
                                    "name": domain_name
                                }
                            }
                        }
                    },
                    "scope": {
                        "domain": {
                            "name": domain_name
                        }
                    }
                }
            }
        else:
            param_data = {
                "username": username,
                "password": password
            }

        headers = api_config.get("headers", {'Content-Type': 'application/json'})

        # 发送认证请求
        try:
            response = request_method(url, data=json.dumps(param_data), verify=False, headers=headers)
            logger.debug(f"Authentication response for {api_name}: {response.text}")
            response.raise_for_status()
            if auth_type == "sc_token":
                # 获取响应头部
                response_headers = response.headers
                token = response_headers.get(token_key, '')
            else:
                # 获取Token
                token_data = response.json()
                token = token_data.get(token_key)
            if token:
                # 更新Token
                api_client['token'] = token
                api_client['token_expires'] = time.time() + token_expiry

                # 更新请求头
                if header_key == "Authorization":
                    api_client['session'].headers.update({
                        header_key: f"Bearer {token}"
                    })
                else:
                    api_client['session'].headers.update({
                        header_key: token
                    })
                logger.info(f"{auth_type.capitalize()} authentication successful for {api_name}")
                return True
            else:
                logger.error(f"Token not found in response for {api_name}. Response: {token_data}")
                return False
        except requests.exceptions.HTTPError as http_err:
            logger.error(f"HTTP error occurred during {auth_type} authentication for {api_name}: {http_err}")
            return False
        except requests.exceptions.RequestException as req_err:
            logger.error(f"Request error occurred during {auth_type} authentication for {api_name}: {req_err}")
            return False
        except ValueError as json_err:
            logger.error(
                f"Error decoding JSON response during {auth_type} authentication for {api_name}: {json_err}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during {auth_type} authentication for {api_name}: {e}")
            return False

    def _save_to_cache(self, key: str, data: Any, expire_time: int = 3600) -> bool:
        """保存数据到缓存"""
        try:
            cache_file = os.path.join(self.cache_dir, f"{key}.json")

            # 创建缓存对象
            cache_obj = {
                'data': data,
                'timestamp': time.time(),
                # 'expire_time': expire_time
            }

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_obj, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving to cache: {e}")
            return False

    def _load_from_cache(self, key: str) -> Optional[Any]:
        """从缓存加载数据"""
        cache_file = os.path.join(self.cache_dir, f"{key}.json")
        if not os.path.exists(cache_file):
            return None

        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_obj = json.load(f)

            # 检查是否过期
            if cache_obj.get('expire_time'):
                current_time = time.time()
                timestamp = cache_obj.get('timestamp', 0)
                expire_time = cache_obj.get('expire_time')

                if current_time - timestamp > expire_time:
                    # 缓存已过期
                    os.remove(cache_file)
                    return None

            return cache_obj.get('data')
        except Exception as e:
            logger.error(f"Error loading from cache: {e}")
            return None

    def initialize_sync(self, force_refresh=False):
        """
        初始化同步过程，包括全量获取数据和模型同步
        """
        logger.info("Starting initialization sync process")

        # 1. 全量获取云内侧数据
        self.fetch_all_cloud_data(force_refresh)

        # 2. 全量获取行内侧数据
        self.fetch_all_internal_data(force_refresh)

        # 3. 根据模型配置进行同步
        self.sync_all_models()

        logger.info("Initialization sync process completed")

    def _apply_transform(self, transform: str, item: Dict[str, Any], related_data: Dict[str, List[Dict[str, Any]]],
                         transform_params: Dict[str, Any] = None, value: Any = None) -> Any:
        """应用转换函数

        Args:
            transform: 转换函数名称
            item: 数据项
            related_data: 关联数据
            transform_params: 转换参数

        Returns:
            转换后的值
        """
        if not transform:
            return None

        logger.debug(f"Applying transform: {transform}")

        # 获取转换函数
        transformer = self.transformers.get(transform)
        if not transformer:
            logger.warning(f"Transformer not found: {transform}")
            return None

        # 获取转换类型
        transform_type = transformer.get('type')

        if transform_type == 'python':
            # Python代码转换
            code = transformer.get('code')
            if not code:
                logger.warning(f"No code found for transformer: {transform}")
                return None

            try:
                # 创建本地命名空间
                local_namespace = {}

                # 执行代码
                exec(code, globals(), local_namespace)

                # 获取转换函数
                func_name = code.strip().split('def ')[1].split('(')[0]
                transform_func = local_namespace.get(func_name)

                if not transform_func:
                    logger.warning(f"Transform function not found in code: {func_name}")
                    return None

                # 检查函数参数
                sig = inspect.signature(transform_func)

                # 准备参数
                kwargs = {'item': item, 'related': related_data}
                if 'params' in sig.parameters:
                    kwargs['params'] = transform_params
                    # kwargs['params']['application_code'] = self.application_code
                    if self.current_model_id in self.internal_related_data and self.internal_related_data[self.current_model_id]:
                        kwargs['params']['internal_related_data'] = self.internal_related_data[self.current_model_id]
                if 'transformers' in sig.parameters:
                    kwargs['transformers'] = self.transformers

                # 应用转换函数
                return transform_func(**kwargs)

            except Exception as e:
                logger.error(f"Error executing transform function: {e}")
                return None

        elif transform_type == 'mapping':
            # 映射转换
            mapping = transformer.get('mapping', {})
            # 应用映射
            default = mapping.get('default')
            if default is None and 'default' not in mapping:
                # 如果没有指定默认值，则使用原始值
                default = value
            # 将值转换为字符串进行映射查找
            str_value = str(value) if value is not None else ''
            mapped_value = mapping.get(str_value, default)
            logger.debug(f"Mapped value '{value}' to '{mapped_value}' using transformer {transform}")
            return mapped_value

        elif transform_type == 'constant':
            # 常量转换，直接返回配置的值
            return transformer.get('value')

        else:
            logger.warning(f"Unsupported transformer type: {transform_type}")
            return None

    def _replace_placeholders(self, obj, item):
        """递归替换对象中的占位符

        Args:
            obj: 要处理的对象
            item: 数据项，用于替换占位符

        Returns:
            处理后的对象
        """
        try:
            logger.debug(f"Replacing placeholders in object type: {type(obj).__name__}")

            if isinstance(obj, str):
                # 替换字符串中的占位符
                if '{' in obj and '}' in obj:
                    original_str = obj
                    for key, value in item.items():
                        placeholder = '{' + key + '}'
                        if placeholder in obj:
                            obj = obj.replace(placeholder, str(value))
                            logger.debug(f"Replaced placeholder {placeholder} with {value}")
                    if original_str != obj:
                        logger.debug(f"String after replacement: {obj[:100]}...")
                    return obj
                return obj
            elif isinstance(obj, dict):
                # 递归处理字典
                logger.debug(f"Replacing placeholders in dictionary with keys: {list(obj.keys())}")
                return {k: self._replace_placeholders(v, item) for k, v in obj.items()}
            elif isinstance(obj, list):
                # 递归处理列表
                logger.debug(f"Replacing placeholders in list of length: {len(obj)}")
                return [self._replace_placeholders(v, item) for v in obj]
            else:
                # 其他类型直接返回
                return obj
        except Exception as e:
            logger.error(f"Error replacing placeholders: {e}")
            # 返回原始对象，避免失败
            return obj

    def _extract_by_path(self, data, path):
        """按路径从数据中提取值

        Args:
            data: 数据对象
            path: 路径，格式为"key1.key2[0].key3"或"key1.key2.0.key3"

        Returns:
            提取的值
        """
        if not path or not data:
            return data

        # 分割路径
        parts = path.split('.')
        result = data

        for part in parts:
            # 处理数组索引，如 key[0]
            match = re.match(r'(\w+)\[(\d+)\]', part)
            if match:
                key, index = match.groups()
                index = int(index)
                if isinstance(result, dict) and key in result:
                    result = result[key]
                    if isinstance(result, list) and 0 <= index < len(result):
                        result = result[index]
                    else:
                        logger.warning(f"Invalid array index {index} for key {key}")
                        return None
                else:
                    logger.warning(f"Key {key} not found in data")
                    return None
            # 处理数字索引，如 0
            elif part.isdigit():
                index = int(part)
                if isinstance(result, list) and 0 <= index < len(result):
                    result = result[index]
                else:
                    logger.warning(f"Invalid array index {index}")
                    return None
            # 处理普通键
            else:
                if isinstance(result, dict) and part in result:
                    result = result[part]
                else:
                    logger.warning(f"Key {part} not found in data")
                    return None

        return result

    def _update_cloud_item(self, endpoint: str, item: Dict[str, Any], source_id: str = None) -> bool:
        """更新云内侧项目

        Args:
            endpoint: 端点名称
            item: 要更新的数据项
            source_id: 数据源ID，如果不提供则使用所有可用的数据源

        Returns:
            更新是否成功
        """
        # 如果没有提供数据源ID，使用所有可用的数据源
        if not source_id:
            cloud_sources = [k.replace('cloud_', '') for k in self.api_clients.keys() if k.startswith('cloud_')]
            if not cloud_sources:
                logger.error("No cloud data source available")
                return False

            # 对所有数据源执行更新操作
            success = True
            for src_id in cloud_sources:
                result = self._update_single_source_item(endpoint, item, src_id)
                success = success and result
            return success
        else:
            # 只更新指定数据源
            return self._update_single_source_item(endpoint, item, source_id)

    def _update_single_source_item(self, endpoint: str, items: Union[Dict[str, Any], List[Dict[str, Any]]],
                                   source_id: str) -> bool:
        """更新单个数据源的云内侧项目，支持批量更新

        Args:
            endpoint: 端点名称
            items: 单个数据项或数据项列表
            source_id: 数据源ID

        Returns:
            是否全部更新成功
        """
        client_key = f"cloud_{source_id}"
        logger.info(f"Updating cloud items with endpoint: {endpoint} via source {source_id}")

        # 将单个项目转换为列表
        if isinstance(items, dict):
            items = [items]

        # 批量处理参数
        batch_size = 20
        success = True

        # 按批次处理数据
        for i in range(0, len(items), batch_size):
            batch_items = items[i:i + batch_size]
            logger.info(
                f"Processing batch {i // batch_size + 1}, items {i + 1} to {min(i + batch_size, len(items))}")
            # 构建批量更新请求数据

            logger.debug(f"Batch update data: {json.dumps(batch_items, ensure_ascii=False)[:500]}...")

            # 使用PUT方法调用API，将批量数据作为请求体
            response = self._call_cloud_api(endpoint, {}, method='PUT', data=batch_items, client_key=client_key)

            batch_success = response is not None
            if batch_success:
                logger.info(f"Successfully updated batch of {len(batch_items)} items via source {source_id}")
            else:
                logger.error(f"Failed to update batch of {len(batch_items)} items via source {source_id}")
                success = False

            # 可选：添加批次间的延迟，避免请求过于频繁
            time.sleep(0.5)

        return success

    def _create_cloud_item(self, endpoint: str, item: Dict[str, Any], source_id: str = None) -> bool:
        """创建云内侧项目

        Args:
            endpoint: 端点名称
            item: 要创建的数据项
            source_id: 数据源ID，如果不提供则使用所有可用的数据源

        Returns:
            创建是否成功
        """
        # 如果没有提供数据源ID，使用所有可用的数据源
        if not source_id:
            cloud_sources = [k.replace('cloud_', '') for k in self.api_clients.keys() if k.startswith('cloud_')]
            if not cloud_sources:
                logger.error("No cloud data source available")
                return False

            # 对所有数据源执行创建操作
            success = True
            for src_id in cloud_sources:
                result = self._create_single_source_item(endpoint, item, src_id)
                success = success and result
            return success
        else:
            # 只在指定数据源创建
            return self._create_single_source_item(endpoint, item, source_id)

    def _create_single_source_item(self, endpoint: str, item: Dict[str, Any], source_id: str) -> bool:
        """在单个数据源创建云内侧项目"""
        client_key = f"cloud_{source_id}"
        logger.info(f"Creating cloud item with endpoint: {endpoint} via source {source_id}")
        logger.debug(f"Create data: {json.dumps(item, ensure_ascii=False)[:500] if item else 'None'}...")

        # 使用POST方法调用API，将item作为请求体数据
        response = self._call_cloud_api(endpoint, {}, method='POST', data=item, client_key=client_key)

        success = response is not None
        if success:
            logger.info(f"Successfully created cloud item via source {source_id}")
        else:
            logger.error(f"Failed to create cloud item via source {source_id}")

        return success

    def _assemble_data(self, model_id, model_config, primary_data, related_data):
        """组装数据

        Args:
            model_id: 模型ID
            model_config: 模型配置
            primary_data: 主模型数据
            related_data: 关联模型数据

        Returns:
            组装后的数据
        """
        logger.info(f"Assembling data for model {model_id}")

        # 获取关联模型配置
        cloud_side_config = model_config.get('cloud_side', {})
        related_models = cloud_side_config.get('related_models', [])

        # 组装数据
        assembled_data = []

        # 处理每个主模型项
        for item in primary_data:
            # 为每个主模型项目添加关联数据
            item_with_related = {
                'primary': item,
                'related': {}
            }
            if not related_models:
                assembled_data.append(item_with_related)
                continue
            # 添加关联模型数据
            for related_model in related_models:
                model_name = related_model.get('model')
                relation_type = related_model.get('relation_type')
                relation_method = related_model.get('relation_method')

                if not model_name or model_name not in related_data:
                    continue

                # 根据关联方式处理
                if relation_method == 'relation_table':
                    # 通过关系表关联的模型
                    source_key = related_model.get('relation_config', {}).get('source_key')
                    target_key = related_model.get('relation_config', {}).get('target_key')

                    if not source_key or not target_key:
                        continue

                    # 查找与当前主模型项相关的目标实体
                    item_id = item.get('id')
                    if not item_id:
                        continue

                    # 查找所有与当前主模型项相关的目标实体
                    related_targets = []

                    for target in related_data.get(model_name, []):
                        # 检查目标实体的关系信息
                        relations = target.get('_relations', [])
                        # 查找与当前主模型项相关的关系
                        if any(rel.get(source_key) == item_id for rel in relations):
                            # 创建目标实体的副本，移除关系信息
                            target_copy = copy.deepcopy(target)
                            target_copy["_relation"] = next(
                                (item for item in relations if item.get(source_key) == item_id), None)
                            if '_relations' in target_copy:
                                del target_copy['_relations']
                            related_targets.append(target_copy)

                    # 根据关联类型处理结果
                    if relation_type == 'one_to_one' and related_targets:
                        # 一对一关系，只取第一个
                        item_with_related['related'][model_name] = related_targets[0]
                    else:
                        # 一对多关系，返回列表
                        item_with_related['related'][model_name] = related_targets

                elif relation_method == 'direct':
                    # 直接关联的模型
                    join_key = related_model.get('join_key')
                    foreign_key = related_model.get('foreign_key')

                    if not join_key or not foreign_key:
                        continue

                    if relation_type == 'one_to_one':
                        # 一对一关系
                        related_item = next((r for r in related_data.get(model_name, [])
                                             if r.get(foreign_key) == item.get(join_key)), None)
                        item_with_related['related'][model_name] = related_item
                    elif relation_type == 'one_to_many':
                        # 一对多关系
                        related_items = [r for r in related_data.get(model_name, [])
                                         if r.get(foreign_key) == item.get(join_key)]
                        item_with_related['related'][model_name] = related_items

            assembled_data.append(item_with_related)

        return assembled_data

    def _fetch_relation_table_data(self, model_id, related_model, primary_data, source_id):
        """通过关系表获取关联模型数据

        Args:
            model_id: 模型ID
            related_model: 关联模型配置
            primary_data: 主模型数据
            source_id: 数据源ID

        Returns:
            关联模型数据列表
        """
        model_name = related_model.get('model')
        logger.info(
            f"Fetching relation table data for model {model_id}, related model {model_name} from source {source_id}")

        # 检查关联方式
        relation_method = related_model.get('relation_method')
        if relation_method and relation_method != 'relation_table':
            logger.debug(f"Model {model_name} is not using relation_table method, skipping")
            return []

        # 获取配置和设置API端点
        config = self._prepare_relation_config(model_id, related_model, model_name)
        if not config:
            return []

        # 获取主模型的关联字段值
        primary_field_values = self._get_primary_field_values(primary_data, config['source_field'])
        if not primary_field_values:
            logger.warning(f"No values found for field {config['source_field']} in model {model_id}")
            return []

        # 获取关系表数据
        all_relations = self._fetch_relations(primary_field_values, config, source_id)
        if not all_relations:
            logger.info(f"No relations found for model {model_id}, related model {model_name}")
            return []

        # 提取目标实体ID
        target_ids = list(set([relation.get(config['target_key']) for relation in all_relations
                               if config['target_key'] in relation]))
        if not target_ids:
            logger.warning(f"No target IDs found in relations for model {model_id}, related model {model_name}")
            return []

        # 获取目标实体数据
        all_targets = self._fetch_targets(target_ids, config, source_id)

        # 为每个目标实体添加关系信息
        return self._attach_relations_to_targets(all_targets, all_relations, config, source_id)

    def _prepare_relation_config(self, model_id, related_model, model_name):
        """准备关系表配置和API端点"""
        # 获取关系表配置
        relation_config = related_model.get('relation_config', {})
        target_config = related_model.get('target_config', {})

        if not relation_config or not target_config:
            logger.warning(f"Missing relation_config or target_config for related model {model_name}")
            return None

        # 组装查询关联模型接口
        relation_model_key = f"relation_{model_name}"
        relation_endpoint = relation_config.get('endpoint')
        self._get_endpoint_url(relation_model_key, model_name)
        # 获取关系表参数
        relation_params = relation_config.get('params', {})
        source_key = relation_config.get('source_key')
        target_key = relation_config.get('target_key')
        relation_result_path = relation_config.get('result_path', 'objList')

        if not relation_endpoint or not source_key or not target_key:
            logger.warning(f"Missing required config in relation_config for related model {model_name}")
            return None

        # 获取目标实体API端点和参数
        target_endpoint = target_config.get('endpoint')
        target_model = target_config.get('model')
        relation_target_model_key = f"get_{target_model}"
        self._get_endpoint_url(relation_target_model_key, target_model)
        target_params = target_config.get('params', {})
        id_key = target_config.get('id_key', 'id')
        target_result_path = target_config.get('result_path', 'objList')

        if not target_endpoint:
            logger.warning(f"Missing endpoint in target_config for related model {model_name}")
            return None

        # 获取主模型的主键字段名
        model_config = self.models.get(model_id, {})
        cloud_side_config = model_config.get('cloud_side', {})
        primary_key = cloud_side_config.get('primary_key', 'id')
        source_field = relation_config.get('source_field', primary_key)

        return {
            'relation_model_key': relation_model_key,
            'relation_params': relation_params,
            'relation_result_path': relation_result_path,
            'target_model_key': relation_target_model_key,
            'target_params': target_params,
            'target_result_path': target_result_path,
            'source_key': source_key,
            'target_key': target_key,
            'id_key': id_key,
            'source_field': source_field
        }

    def _get_primary_field_values(self, primary_data, source_field):
        """获取主模型的关联字段值"""
        return [item.get(source_field) for item in primary_data if source_field in item]

    def _fetch_relations(self, primary_field_values, config, source_id):
        """获取关系表数据"""
        batch_size = 20
        all_relations = []
        client_key = f"cloud_{source_id}"

        for i in range(0, len(primary_field_values), batch_size):
            batch_values = primary_field_values[i:i + batch_size]

            # 构建批量查询参数
            batch_params = copy.deepcopy(config['relation_params'])
            # 替换参数中的占位符
            batch_params = self._replace_placeholders(batch_params, {config['source_field']: batch_values})

            # 添加分页参数
            page_no = 1
            has_more = True
            while has_more:
                # 设置当前页码和每页大小
                batch_params['pageNo'] = page_no
                batch_params['pageSize'] = batch_size

                # 查询关系表
                relations = self._call_cloud_api(config['relation_model_key'], batch_params,
                                                 method='GET', client_key=client_key)

                if not relations:
                    break

                # 提取关系数据，使用配置中的result_path
                relation_list = self._extract_by_path(relations, config['relation_result_path'])
                if relation_list:
                    all_relations.extend(relation_list)

                total_pages = relations.get('totalPageNo', 1)
                current_page = relations.get('currentPage', 1)

                logger.debug(f"Retrieved page {current_page} of {total_pages} for target data")

                # 如果当前页小于总页数，继续获取下一页
                if current_page < total_pages:
                    page_no += 1
                else:
                    has_more = False

        return all_relations

    def _fetch_targets(self, target_ids, config, source_id):
        """获取目标实体数据"""
        batch_size = 50
        all_targets = []
        client_key = f"cloud_{source_id}"

        for i in range(0, len(target_ids), batch_size):
            batch_target_ids = target_ids[i:i + batch_size]

            # 构建批量查询参数
            batch_params = copy.deepcopy(config['target_params'])
            # 替换参数中的占位符
            batch_params = self._replace_placeholders(batch_params, {'target_ids': batch_target_ids})
            # 添加分页参数
            page_no = 1
            has_more = True
            while has_more:
                # 设置当前页码和每页大小
                batch_params['pageNo'] = page_no
                batch_params['pageSize'] = batch_size

                # 查询目标实体
                targets = self._call_cloud_api(config['target_model_key'], batch_params, client_key=client_key)

                if not targets:
                    break

                # 提取目标实体数据，使用配置中的result_path
                target_list = self._extract_by_path(targets, config['target_result_path'])
                if target_list:
                    all_targets.extend(target_list)

                # 检查是否有更多页
                total_pages = targets.get('totalPageNo', 1)
                current_page = targets.get('currentPage', 1)

                logger.debug(f"Retrieved page {current_page} of {total_pages} for target data")

                # 如果当前页小于总页数，继续获取下一页
                if current_page < total_pages:
                    page_no += 1
                else:
                    has_more = False

        return all_targets

    def _prepare_request_params(self, params):
        """准备API请求参数，处理列表转换和复杂参数结构

        功能：
        1. 将字符串形式的列表值转换为实际的Python列表
        2. 将复杂的嵌套参数结构（如condition）转换为适合GET请求的格式

        Args:
            params: 原始请求参数字典

        Returns:
            处理后的请求参数字典
        """
        if not params or not isinstance(params, dict):
            return params

        # 检查并转换constraint中的value为列表类型
        if 'condition' in params and 'constraint' in params['condition']:
            for constraint in params['condition']['constraint']:
                if 'simple' in constraint and 'value' in constraint['simple']:
                    value = constraint['simple']['value']
                    # 如果value是字符串且看起来像列表字符串，则转换为实际的列表
                    if isinstance(value, str) and value.startswith('[') and value.endswith(']'):
                        try:
                            constraint['simple']['value'] = ast.literal_eval(value)
                        except (SyntaxError, ValueError) as e:
                            logger.warning(f"Failed to convert value string to list: {e}")

        # 处理GET请求的复杂参数
        # 将condition参数转换为JSON字符串
        if 'condition' in params:
            condition_json = json.dumps(params.get('condition'))
            # 创建新的参数字典，移除原始的condition
            simple_params = {k: v for k, v in params.items() if k != 'condition'}
            # 添加condition作为字符串参数
            simple_params['condition'] = condition_json
            return simple_params

        return params

    def _attach_relations_to_targets(self, all_targets, all_relations, config, source_id):
        """为目标实体添加关系信息"""
        for target in all_targets:
            target_id = target.get(config['id_key'])
            if target_id:
                # 查找与该目标实体相关的所有关系
                related_relations = [rel for rel in all_relations if rel.get(config['target_key']) == target_id]
                # 添加关系信息
                target['_relations'] = related_relations
                # 添加数据源标识
                target['_source_id'] = source_id

        return all_targets

    def _prepare_batch_internal_data(self, internal_side_config: Dict[str, Any],
                                     items: List[Dict[str, Any]]):
        """准备批量行内侧数据封装"""
        internal_primary_key = internal_side_config.get("primary_key")
        update_params = internal_side_config.get('update_params', {})
        api_config = update_params.get('api_config', {})
        field_wrapper = internal_side_config.get('field_wrapper', {})

        # 获取类名
        class_name = update_params.get('className', internal_side_config.get('model'))

        # 更新类名不存在 配置错误 直接返回报错即可
        # if not class_name:
        #     return items

        # 检查请求格式
        request_format = api_config.get('request_format')

        if request_format == 'nested':
            # 处理嵌套结构
            nested_config = api_config.get('nested_config', {})
            root_key = nested_config.get('root_key')
            class_key = nested_config.get('class_key', class_name)

            # 获取固定字段名和值
            constant_key = nested_config.get('constant_key')
            constant_value = nested_config.get('constant_value', internal_primary_key)

            # 处理每个项目
            processed_items = []
            for item in items:
                processed_data = item.copy()

                # 添加固定字段
                if constant_key:
                    processed_data[constant_key] = constant_value

                # 处理外键字段
                if field_wrapper.get('enabled', False):
                    foreign_key_wrapper = field_wrapper.get('foreign_key_wrapper')
                    if foreign_key_wrapper:
                        # 提取外键字段
                        foreign_keys = {}
                        for key, value in list(processed_data.items()):
                            if isinstance(value, dict) and key != foreign_key_wrapper:
                                foreign_keys[key] = value
                                del processed_data[key]

                        if foreign_keys:
                            processed_data[foreign_key_wrapper] = foreign_keys

                processed_items.append(processed_data)

            # 构建批量嵌套结构
            return {
                root_key: {
                    class_key: processed_items
                }
            }

        # 默认情况下，直接返回数据列表
        return items

    def _prepare_internal_update_data(self, internal_side_config: Dict[str, Any], item: Dict[str, Any]) -> Dict[
        str, Any]:
        """准备单个行内侧更新数据"""
        # 调用批量处理方法，但只传入一个项目
        result = self._prepare_batch_internal_data(internal_side_config, [item])

        # 如果结果是字典（嵌套结构），直接返回
        if isinstance(result, dict):
            return result

        # 如果结果是列表，返回第一个项目
        if isinstance(result, list) and len(result) > 0:
            return result[0]

        return item

    def _batch_update_internal_items(self, endpoint: str, internal_side_config: Dict[str, Any],
                                     items: List[Dict[str, Any]], batch_size: int = 30, sync_record=None, retry=False, operation_type='created') -> bool:
        """批量更新行内侧项目"""
        if not items:
            return True

        success = True

        # 按批次处理
        for i in range(0, len(items), batch_size):
            batch_items = items[i:i + batch_size]

            # 使用提取的方法准备批量数据
            request_data = self._prepare_batch_internal_data(internal_side_config, batch_items)

            # # 如果返回的是列表（非嵌套结构），则逐个处理
            # if isinstance(request_data, list):
            #     for item in request_data:
            #         response = self._call_internal_api(endpoint, {}, method='POST', data=item)
            #         if response is not None and 'ERROR' in response.get('msg', ''):
            #             success = False
            #             logger.error(response.get('msg', ''))
            #             if sync_record:
            #                 sync_record.failed_count += 1
            #         elif sync_record:
            #             sync_record.success_count += 1
            #             # 获取记录ID用于记录变更
            #             logger.info(f"operating internal api result: {response.get('msg', '')}")
            #             primary_key = internal_side_config.get('primary_key')
            #             if primary_key and primary_key in item:
            #                 record_id = item.get(primary_key)
            #                 operation = "created" if "id" not in item else "updated"
            #                 sync_record.add_change(record_id, "operation", None, operation)
            # else:
            # 如果是字典（嵌套结构），则批量处理
            response = self._call_internal_api(endpoint, {}, method='POST', data=request_data)

            # 使用统一的响应处理方法
            is_success, error_message = self._handle_api_response(response, "批量更新操作", log_success=False)

            if not is_success:
                success = False
                if retry:
                    if sync_record:
                        sync_record.add_failed(len(batch_items))
                else:
                    logger.error(f"将报错数据加入错误数据集！数量：{len(batch_items)}")
                    self._add_failed_request_data_to_retry_collection(request_data, batch_items)

            elif sync_record:
                # 一次遍历完成数据源统计和变更记录
                source_counts = {}
                primary_key = internal_side_config.get('primary_key')

                for item in batch_items:
                    # 数据源统计
                    source_id = item.get('_source_id')
                    if source_id:
                        source_counts[source_id] = source_counts.get(source_id, 0) + 1

                    # # 记录变更
                    # if primary_key and primary_key in item:
                    #     record_id = item.get(primary_key)
                    #     logger.info(f"变更数据入库留存，数据record_id：{primary_key}")
                    #     sync_record.add_change(record_id, None, item, operation_type)

                # 统计成功数量（不传source_id，避免重复统计）
                sync_record.add_success(len(batch_items), operation_type, None)

                # 手动添加数据源统计
                for source_id, count in source_counts.items():
                    sync_record.add_source_statistic(source_id, 'success', count)
                    sync_record.add_source_statistic(source_id, operation_type, count)

                # 记录批量操作结果
                logger.info(f"operating internal api result: {self._safe_get_response_field(response, 'msg', '操作成功')}")

        return success

    def _batch_delete_internal_items(self, endpoint: str, internal_side_config: Dict[str, Any],
                                     items: List[Dict[str, Any]], batch_size: int = 50, sync_record=None, retry=False) -> bool:
        """批量删除行内侧项目

        Args:
            endpoint: 删除端点
            internal_side_config: 行内侧配置
            items: 要删除的项目列表
            batch_size: 批量大小
            sync_record: 同步记录对象

        Returns:
            是否成功
        """
        if not items:
            return True

        success = True

        # 获取删除参数配置
        delete_params = internal_side_config.get('delete_params', {})
        api_config = delete_params.get('api_config', {})
        request_format = api_config.get('request_format', 'default')

        # 按批次处理
        for i in range(0, len(items), batch_size):
            batch_items = items[i:i + batch_size]

            # 根据请求格式构建删除请求数据
            if request_format == 'record_list':
                # 使用记录列表格式
                record_config = api_config.get('record_config', {})
                id_field = record_config.get('id_field', 'recordId')
                table_field = record_config.get('table_field', 'tableName')

                # 构建记录列表
                records = []
                for item in batch_items:
                    record_id = item.get("id")
                    if record_id:
                        records.append({
                            id_field: record_id,
                            table_field: delete_params.get('className', internal_side_config.get('model'))
                        })

                if not records:
                    continue

                # 调用删除接口
                response = self._call_internal_api(endpoint, {}, method='POST', data=records)

                # 使用统一的响应处理方法
                is_success, message = self._handle_api_response(response, "批量删除操作")

                if not is_success:
                    success = False
                    if retry:
                        if sync_record:
                            sync_record.add_failed(len(batch_items))
                            sync_record.errors.append({
                                'error_type': 'sync_error',
                                'error_message': str({'msg':message, 'data':batch_items}),
                                'time': datetime.now(),
                                'record_id': None,
                                'stack_trace': None
                            })
                    else:
                        logger.error(f"将报错操作的数据加入收集数据集！数量：{len(records)}")
                        self._add_failed_delete_data_to_retry_collection(batch_items)
                else:
                    # API调用成功
                    logger.info(f"Successfully deleted {len(records)} items using record list format")
                    if sync_record:
                        # 一次遍历完成数据源统计和删除记录（性能优化）
                        source_counts = {}
                        primary_key = internal_side_config.get('primary_key')

                        for item in batch_items:
                            # 数据源统计


                            source_id = item.get('_source_id')
                            if source_id:
                                source_counts[source_id] = source_counts.get(source_id, 0) + 1

                            # 记录删除操作（在同一次遍历中完成）
                            record_id = item.get(primary_key) if primary_key else item.get("id")
                            if record_id:
                                # sync_record.add_change(record_id, "delete_operation", "existed", "deleted")
                                self._record_field_changes(record_id, item, None, sync_record=sync_record, operation_type="deleted")
                            else:
                                logger.warning(f"删除项目缺少主键字段 {primary_key}: {item}")

                        # 统计成功数量（不传source_id，避免重复统计）
                        sync_record.add_success(len(batch_items), 'deleted', None)

                        # 手动添加数据源统计
                        for source_id, count in source_counts.items():
                            sync_record.add_source_statistic(source_id, 'success', count)
                            sync_record.add_source_statistic(source_id, 'deleted', count)
            else:
                success = False
                logger.error(f"Request format [{request_format}] not supported for delete operation!")
                if sync_record:
                    sync_record.add_failed(len(batch_items))

        # 注意：删除错误数据的清理现在在重试方法中处理

        return success

    def _get_internal_key(self, item: Dict[str, Any], primary_key: str) -> Optional[str]:
        """获取数据主键

        Args:
            item: 数据项
            primary_key: 主键字段名

        Returns:
            主键值，如果没有则返回None
        """
        if not item or not primary_key:
            return None

        # 直接获取主键值
        key = item.get(primary_key)

        # 确保键是字符串类型
        if key is not None:
            return str(key)

        return None

    def _fetch_cloud_data_with_params(self, endpoint: str, model_config: Dict[str, Any],
                                      additional_params: Dict[str, Any], source_id: str = None) -> List[Dict[str, Any]]:
        """使用额外参数获取云内侧数据

        这个方法复用_fetch_cloud_data方法，但允许传入额外的查询参数

        Args:
            endpoint: 端点名称
            model_config: 模型配置
            additional_params: 额外的查询参数
            source_id: 数据源ID，如果提供则使用指定的数据源

        Returns:
            获取的数据列表
        """
        # 获取云内侧配置
        cloud_side_config = model_config.get('cloud_side', {})
        api_config = cloud_side_config.get('api_config', {})

        # 合并API配置中的参数和额外参数
        base_params = copy.deepcopy(api_config.get('params', {}))

        # 递归合并字典
        def merge_dicts(d1, d2):
            for k, v in d2.items():
                if k in d1 and isinstance(d1[k], dict) and isinstance(v, dict):
                    merge_dicts(d1[k], v)
                else:
                    d1[k] = v

        # 合并参数
        merge_dicts(base_params, additional_params)

        # 调用现有的_fetch_cloud_data方法，但不使用其默认分页逻辑
        # 因为我们已经在base_params中设置了所有需要的参数
        all_data = []
        page_no = 1
        batch_size = model_config.get('batch_size', 100)

        # 获取分页配置
        pagination_config = api_config.get('pagination', {})
        total_pages_field = pagination_config.get('total_pages_field', 'totalPageNo')
        page_field = pagination_config.get('page_field', 'pageNo')
        size_field = pagination_config.get('size_field', 'pageSize')
        result_path = api_config.get('result_path', 'objList')

        # 确保分页参数存在
        if page_field not in base_params:
            base_params[page_field] = page_no
        if size_field not in base_params:
            base_params[size_field] = batch_size

        while True:
            # 更新页码
            base_params[page_field] = page_no

            # 调用API，传入数据源ID
            response = self._call_cloud_api(endpoint, base_params,
                                            client_key=f"cloud_{source_id}" if source_id else None)
            if not response:
                break

            # 提取数据
            data = self._extract_by_path(response, result_path)
            if not data:
                break

            # 添加到结果
            all_data.extend(data)

            # 检查是否还有下一页
            total_pages = self._safe_get_response_field(response, total_pages_field)

            # 如果找不到总页数字段，尝试检查数据量是否小于每页大小，如果是则认为没有下一页
            if total_pages is None:
                if len(data) < batch_size:
                    break
            elif page_no >= total_pages:
                break

            # 下一页
            page_no += 1

        return all_data

    def _fetch_related_data(self, endpoint: str, primary_data: List[Dict[str, Any]],
                            related_model: Dict[str, Any], source_id: str = None) -> List[Dict[str, Any]]:
        """获取关联模型数据

        这个方法根据关联模型的配置和主模型数据获取关联数据

        Args:
            endpoint: 关联模型端点
            primary_data: 主模型数据
            related_model: 关联模型配置
            source_id: 数据源ID

        Returns:
            关联模型数据
        """
        # 检查关联方式
        relation_method = related_model.get('relation_method')

        if relation_method == 'relation_table':
            # 通过关系表关联的模型
            return self._fetch_relation_table_data(self.current_model_id, related_model, primary_data, source_id)
        elif relation_method == 'direct':
            # 直接关联的模型
            # 获取关联字段
            join_key = related_model.get('join_key')
            foreign_key = related_model.get('foreign_key')

            if not join_key or not foreign_key:
                logger.warning(f"Missing join_key or foreign_key for related model {related_model.get('model')}")
                return []

            # 获取所有主模型的关联字段值
            join_values = [item.get(join_key) for item in primary_data if
                           join_key in item and item.get(join_key) is not None]

            if not join_values:
                logger.warning(f"No values found for join field {join_key} in primary data")
                return []

            # 去重
            join_values = list(set(join_values))

            # 构建查询参数
            params = related_model.get('params', {})

            # 添加过滤条件
            if 'condition' not in params:
                params['condition'] = {}
            if 'constraint' not in params['condition']:
                params['condition']['constraint'] = []

            # 添加关联字段过滤
            params['condition']['constraint'].append({
                'simple': {
                    'name': foreign_key,
                    'value': join_values,
                    'operator': 'in'
                }
            })

            # 调用API获取关联数据
            return self._fetch_cloud_data_with_params(endpoint, {'batch_size': 1000}, params, source_id)
        else:
            logger.warning(f"Unknown relation method: {relation_method}")
            return []

    def _validate_item(self, item, model_config, field=None, temp_value=None):
        """验证项目是否符合规则

        Args:
            item: 要验证的项目
            model_config: 模型配置
            field: 指定字段名（用于单字段验证）
            temp_value: 临时值（可选，用于验证）

        Returns:
            bool: 验证结果
        """
        # 获取过滤检查配置
        filter_checks = model_config.get('filter_checks', [])
        if not filter_checks:
            return True

        # 创建验证器
        validator = FilterValidator(filter_checks)

        # 如果提供了临时值，将其添加到item的副本中用于验证
        if temp_value is not None and field is not None:
            # 创建item的副本，避免修改原始数据
            validation_item = item.copy() if isinstance(item, dict) else {}
            # 将临时值添加到副本中
            validation_item[field] = temp_value
            item_to_validate = validation_item
        else:
            item_to_validate = item

        # 如果指定了字段，只验证该字段的规则
        if field:
            is_valid, errors = validator.validate_field(item_to_validate, field)
        else:
            # 否则验证整个记录
            is_valid, errors = validator.validate_record(item_to_validate)

        if not is_valid:
            logger.warning(f"Item validation failed: {errors}")

        return is_valid

    def _determine_sync_direction(self, model_config: Dict[str, Any]) -> str:
        """
        根据模型配置中的字段映射确定同步方向

        Args:
            model_config: 模型配置

        Returns:
            同步方向: 'bidirectional', 'cloud_to_internal', 或 'internal_to_cloud'
        """
        field_mappings = model_config.get('field_mappings', [])

        # 检查是否存在不同方向的字段映射
        has_cloud_to_internal = any(m.get('sync_direction') == 'cloud_to_internal' for m in field_mappings)
        has_internal_to_cloud = any(m.get('sync_direction') == 'internal_to_cloud' for m in field_mappings)
        has_bidirectional = any(m.get('sync_direction') == 'bidirectional' for m in field_mappings)

        # 确定整体同步方向
        if has_bidirectional or (has_cloud_to_internal and has_internal_to_cloud):
            return 'bidirectional'
        elif has_cloud_to_internal:
            return 'cloud_to_internal'
        elif has_internal_to_cloud:
            return 'internal_to_cloud'
        else:
            return 'bidirectional'  # 默认为双向

    def _get_endpoint_url(self, endpoint_key, model_name, create_if_missing=True):
        """获取或创建端点URL

        根据端点键名和模型名称获取或创建API端点URL。
        如果端点已存在，直接返回；如果不存在且允许创建，则根据端点类型创建新的URL。

        Args:
            endpoint_key: 端点键名，如"get_VM"、"update_VM"、"relation_R_Volume_MountOn_VM"
            model_name: 模型名称，用于替换URL模板中的占位符
            create_if_missing: 如果端点不存在，是否创建新端点

        Returns:
            端点URL字符串，如果不存在且不允许创建则返回None
        """
        # 检查端点是否已存在
        cloud_side_endpoints = self.endpoints.get("cloud_side", {})
        if endpoint_key in cloud_side_endpoints:
            return cloud_side_endpoints.get(endpoint_key)

        # 如果不存在且不允许创建，直接返回None
        if not create_if_missing:
            return None

        # 确定基础端点模板和替换规则
        if endpoint_key.startswith("relation_") or endpoint_key.startswith('update_relation_'):
            # 关系API使用relationName替换
            base_endpoint = "cloud_relation_api"
            placeholder = "${relationName}"
        else:
            # 模型API使用className替换
            base_endpoint = "cloud_cmdb_model"
            placeholder = "${className}"

        # 获取基础端点模板
        base_url_template = cloud_side_endpoints.get(base_endpoint, "")
        if not base_url_template:
            logger.warning(f"Base endpoint template '{base_endpoint}' not found")
            return None

        # 创建并存储新端点
        endpoint_url = base_url_template.replace(placeholder, model_name)
        cloud_side_endpoints[endpoint_key] = endpoint_url
        logger.debug(f"Created new endpoint URL for {endpoint_key}: {endpoint_url}")

        return endpoint_url

    def _get_sync_limits(self, model_id, sync_direction):
        """
        获取指定模型和同步方向的同步数量限制

        Args:
            model_id: 模型ID
            sync_direction: 同步方向，'cloud_to_internal' 或 'internal_to_cloud'

        Returns:
            dict: 包含各种操作限制的字典
        """
        # 获取默认限制
        default_limits = self.base_config.get('default_sync_limits', {}).get(sync_direction, {})

        # 获取模型特定限制
        model_limits = self.base_config.get('model_sync_limits', {}).get(model_id, {}).get(sync_direction, {})

        # 合并限制，模型特定限制优先
        limits = {
            'update': model_limits.get('update', default_limits.get('update', 0)),
            'create': model_limits.get('create', default_limits.get('create', 0)),
            'delete': model_limits.get('delete', default_limits.get('delete', 0))
        }

        return limits

    def _apply_operation_limits(self, items, limit, operation_type, model_id, sync_direction):
        """
        应用操作特定的同步数量限制

        Args:
            items: 操作项目列表
            limit: 操作限制数量
            operation_type: 操作类型
            model_id: 模型ID
            sync_direction: 同步方向

        Returns:
            list: 限制后的项目列表
        """
        if limit <= 0 or len(items) <= limit:
            return items

        logger.info(
            f"{operation_type.capitalize()} operations ({len(items)}) exceed limit ({limit}) "
            f"for model {model_id} in {sync_direction} direction, limiting"
        )

        return items[:limit]

    def _get_deletion_items(self, model_id, internal_data_index, processed_keys, resource_type, could_match_key):
        """获取需要删除的项目"""
        delete_items = []

        # 如果不是云资源类型，不执行删除
        if resource_type != 'cloud':
            return delete_items

        # 获取上一个任务周期的云内数据
        previous_cloud_data = None
        if model_id in self.history_data and 'cloud_data' in self.history_data[model_id]:
            previous_cloud_data = self.history_data[model_id]['cloud_data']

        if previous_cloud_data:
            # 从历史数据中提取所有云内资源的主键 需要使用云内的匹配符字段
            previous_cloud_keys = self._extract_previous_cloud_keys(previous_cloud_data, could_match_key)

            # 找出上一周期存在但本周期不存在的资源
            for key, item in internal_data_index.items():
                # 只处理上一周期由云内上报的资源
                if key in previous_cloud_keys and key not in processed_keys:
                    delete_items.append(item)
                    logger.info(
                        f"Cloud resource with key {key} existed in previous cycle but not found in current cloud data, will be deleted")

        return delete_items

    def sync_cloud_firewall_policy_log(self, sync_record=None, force_refresh=False):
        firewall_policy_log_config = self.models.get('firewall_policy_log')
        # 如果没有传入同步记录，创建一个新的
        if sync_record is None:
            sync_direction = self._determine_sync_direction(firewall_policy_log_config)
            sync_record = SyncRecord('firewall_policy_log', 'full', sync_direction)
            sync_record.source_id = firewall_policy_log_config.get('cloud_side', {}).get('source_id')
        if not firewall_policy_log_config.get("sync_enabled"):
            return sync_record
        # 先根据配置的数据源 确定这个模型同步的数据源有哪些
        sources_list = self._get_model_sources('firewall_policy_log')
        field_mappings = firewall_policy_log_config.get('field_mappings')
        if not sources_list:
            logger.info("No sources found for firewall_policy_log")
        # 检查缓存中是否有数据且不强制刷新
        if "firewall_policy_log" in self.cloud_data and not force_refresh:
            cloud_data = self.cloud_data.get("firewall_policy_log")
        else:
            # 获取到所有需要的数据
            cloud_data = self.get_cloud_model_data(firewall_policy_log_config, sources_list)
        if not cloud_data:
            logger.info("No data collect in Cloud, cloud not synced Cloud Firewall Policy Log  successfully")

        # 如果数据不为空  先保存一下数据
        self._save_to_cache(f"cloud_firewall_policy_log", cloud_data)
        # 开始调用解析数据的方法 获取到组装后的数据
        sync_firewall_policy_log_list = self.analysis_cloud_data(cloud_data, firewall_policy_log_config)
        if not sync_firewall_policy_log_list:
            logger.info("No data collect in Cloud Firewall Policy Log successfully")
        # 获取行内的数据
        internal_data = self.fetch_all_internal_data(model_id="firewall_policy_log")
        model_id = "firewall_policy_log"
        # 这里调用上报数据接口 进行上报
        self._sync_cloud_to_internal("firewall_policy_log", firewall_policy_log_config,
                                     sync_firewall_policy_log_list, internal_data.get("firewall_policy_log"), field_mappings, sync_record)
        # 同步完成后，保存当前数据作为历史数据
        self.history_data[model_id] = {
            'cloud_data': self.cloud_data.get(model_id, {}),
            'internal_data': self.internal_data.get(model_id),
            'sync_data': self.sync_data.get(model_id, {}),
            'timestamp': time.time()
        }

        self._save_to_cache(f"history_firewall_policy_log", self.history_data['firewall_policy_log'], expire_time=86400)

        # 同步完成后更新记录状态
        sync_record.complete('success')
        # 保存同步记录
        self.record_manager.save_record(sync_record)
        return sync_record

    def analysis_cloud_data(self, cloud_data,firewall_policy_log_config):

        sync_firewall_policy_log_list = []
        # 第一层循环 获取到每个数据源的数据
        for sourc_key, source_data in cloud_data.items():

            # 这个source_data 里面存的是 primary 主数据 related：{"key":[],"key2":[]}
            primary_data = source_data.get('primary')  # 这个是网络ACL数据
            if not primary_data:
                logger.info("No primary data found for source_data, Skipping")
                continue
            eps_ids = [item["epsId"] for item in primary_data if "epsId" in item and item["epsId"] != "0"]
            application_dict = self.get_application_by_eps_ids(eps_ids, sourc_key, firewall_policy_log_config)
            self.application_code.update(application_dict)
            # 这里来拿关联模型数据和 关联关系数据
            related_model = source_data.get('related')
            if not related_model:
                logger.info("No related data found for source_data, Skipping")
                continue
            # 这里就需要分两个方法去组装数据 一个方法用网络策略和网络策略规则 形成新的数据 一个用端口 安全组 安全组规则形成新的数据
            cloud_vfw_policy = related_model.get("CLOUD_VFW_POLICY")
            cloud_vfw_rule = related_model.get("CLOUD_VFW_RULE")
            cloud_security_group = related_model.get("CLOUD_SECURITY_GROUP")
            cloud_security_group_rule = related_model.get("CLOUD_SECURITY_GROUP_RULE")
            cloud_port = related_model.get("CLOUD_PORT")
            r_vfw_use_policy = related_model.get("R_VFW_USE_POLICY")
            r_vfw_policy_use_rule = related_model.get("R_VFW_POLICY_USE_RULE")
            r_vfw_use_port = related_model.get("R_VFW_USE_PORT")
            r_port_use_security_group = related_model.get("R_PORT_USE_SECURITY_GROUP")
            # ACL策略台账数据
            vfw_rule_list = self.package_table_by_vfw_rule(primary_data, cloud_vfw_policy, cloud_vfw_rule,
                                                           r_vfw_use_policy,
                                                           r_vfw_policy_use_rule, sourc_key)
            sync_firewall_policy_log_list.extend(vfw_rule_list)
            # 安全组策略台账数据
            vfw_port_list = self.package_table_by_port_security(primary_data, cloud_port, cloud_security_group,
                                                                cloud_security_group_rule, r_port_use_security_group,
                                                                r_vfw_use_port, sourc_key)
            sync_firewall_policy_log_list.extend(vfw_port_list)

        return sync_firewall_policy_log_list

    def mapping_field(self, firewall_policy_log_list, field_mappings):
        # 预先构建映射字典，提高查找效率
        cloud_to_internal_map = {
            field["cloud_field"]: field["internal_field"]
            for field in field_mappings
            if field.get("sync_direction") == "cloud_to_internal"
        }

        firewall_policy_log_data = []
        for firewall_policy_log in firewall_policy_log_list:
            # 使用字典推导式和预先构建的映射关系进行转换
            firewall_policy_log_dict = {
                cloud_to_internal_map.get(key, key): value
                for key, value in firewall_policy_log.items()
                if key in cloud_to_internal_map
            }
            firewall_policy_log_data.append(firewall_policy_log_dict)

        return firewall_policy_log_data

    def package_table_by_vfw_rule(self, primary_data, cloud_vfw_policy, cloud_vfw_rule, r_vfw_use_policy,
                                  r_vfw_policy_use_rule, sourc_key):
        """
            primary_data 网络ACL 数据
            cloud_vfw_policy 网络ACL策略 数据
            cloud_vfw_rule 网络ACL策略规则 数据
            r_vfw_use_policy 网络ACL和网络ACL策略关联关系 多对多关系
            r_vfw_policy_use_rule 网络ACL策略和网络ACL策略规则 关联关系 多对多
            sourc_key 数据源
        """
        # 先组装一下网络策略ACL和网络策略之间的数据形成一张表
        # 创建 VFW_USE_POLICY_DATA
        # 任何一张表 没有数据都没办法完成功能ACL 策略台账
        if not primary_data or not cloud_vfw_rule or not cloud_vfw_policy or not r_vfw_use_policy or not r_vfw_policy_use_rule:
            return []
        vfw_use_policy_data = []
        for policy in r_vfw_use_policy:
            vfw = next((v for v in primary_data if v['id'] == policy['vfwId']), None)
            vfw_policy = next((p for p in cloud_vfw_policy if p['id'] == policy['policyId']), None)
            if vfw and vfw_policy:
                new_entry = {
                    "name": vfw.get('name'),
                    "id": vfw.get('id'),
                    "description": vfw.get('description'),
                    "logicalRegionId": vfw.get('logicalRegionId'),
                    "logicalRegionName": vfw.get('logicalRegionName'),
                    "tenantName": vfw.get('tenantName'),
                    "vdcName": vfw.get('vdcName'),
                    "ruleType": policy.get("ruleType"),
                    "class_Name": "ACL",
                    "policyId": policy.get("policyId")
                }
                vfw_use_policy_data.append(new_entry)

        # 创建 VFW_POLICY_USE_RULE_DATA
        vfw_policy_use_rule_data = []
        for rule_relation in r_vfw_policy_use_rule:
            vfw_policy = next((p for p in cloud_vfw_policy if p['id'] == rule_relation['policyId']), None)
            vfw_rule = next((r for r in cloud_vfw_rule if r['id'] == rule_relation['ruleId']), None)
            if vfw_policy and vfw_rule:
                new_entry = {
                    "policyId": rule_relation.get("policyId"),
                    "ruleId": vfw_rule.get("id"),
                    "ruleName": vfw_rule.get("name"),
                    "ipAddressType": self.check_ip_version(vfw_rule.get("ipAddress")),
                    "enable": vfw_rule.get("enabled"),
                    "position": vfw_rule.get("position"),
                    "action": vfw_rule.get("action"),
                    "protocol": vfw_rule.get("protocol"),
                    "ipAddress": vfw_rule.get("ipAddress"),
                    "port": vfw_rule.get("port"),
                    "destinationIpAddress": vfw_rule.get("destinationIpAddress"),
                    "destinationPort": vfw_rule.get("destinationPort"),
                }
                vfw_policy_use_rule_data.append(new_entry)
        # 优化：使用字典按policyId分组vfw_policy_use_rule_data
        policy_id_to_rules = {}
        for rule in vfw_policy_use_rule_data:
            policy_id = rule.get("policyId")
            if policy_id not in policy_id_to_rules:
                policy_id_to_rules[policy_id] = []
            policy_id_to_rules[policy_id].append(rule)

        # 现在根据这两个list 将数据再一次进行组装  组装的关键字为 policyId
        all_vfw_policy_rule_list = []
        for vfw_policy in vfw_use_policy_data:
            vfw_policy_id = vfw_policy.get("policyId")
            # 使用字典快速查找匹配的规则
            if vfw_policy_id in policy_id_to_rules:
                # 对每个匹配的规则，合并数据
                for rule in policy_id_to_rules[vfw_policy_id]:
                    all_vfw_policy_rule_list.append(
                        {"related": {}, "_source_id": sourc_key, "primary": {**vfw_policy, **rule}})
        return all_vfw_policy_rule_list

    def package_table_by_port_security(self, primary_data, cloud_port, cloud_security_group, cloud_security_group_rule,
                                       r_port_use_security_group,
                                       r_vfw_use_ports, source_key):
        """
            primary_data 网络ACL 数据
            cloud_port 端口
            r_vfw_use_port 网络ACL策略对应端口关联关系 多对多
            cloud_security_group 安全组
            cloud_security_group_rule 安全组规则
            r_port_use_security_group 端口和安全组关联关系  多对多关心
        """
        # 先组装端口和网络ACL 关系表
        # 任何一张表没有数据都没办法完成
        if not primary_data or not cloud_port or not cloud_security_group or not cloud_security_group_rule or not r_port_use_security_group or not r_vfw_use_ports:
            return []
        vfw_port_list = []
        for r_vfw_use_port in r_vfw_use_ports:
            vfw = next((v for v in primary_data if v['id'] == r_vfw_use_port['vfwId']), None)
            vfw_port = next((p for p in cloud_port if p['id'] == r_vfw_use_port['portId']), None)
            if vfw and vfw_port:
                vfw_port_dict = {
                    "name": vfw.get('name'),
                    "id": vfw.get('id'),
                    "epsId": vfw.get('epsId'),
                    # "ruleId": vfw_port.get("id"),
                    "ruleName": vfw_port.get("name"),
                    "description": vfw.get('description'),
                    "logicalRegionId": vfw.get('logicalRegionId'),
                    "logicalRegionName": vfw.get('logicalRegionName'),
                    "tenantName": vfw.get('tenantName'),
                    "vdcName": vfw.get('vdcName'),
                    "class_Name": "安全组",
                    "portId": r_vfw_use_port.get("portId")
                }
                vfw_port_list.append(vfw_port_dict)

        # 优化：预先构建安全组ID到规则的映射
        security_group_id_to_rules = {}
        for rule in cloud_security_group_rule:
            sg_id = rule.get("securityGroupId")
            if sg_id not in security_group_id_to_rules:
                security_group_id_to_rules[sg_id] = []
            security_group_id_to_rules[sg_id].append(rule)

        # 优化：使用字典存储portId到securityGroup的映射，避免重复查询
        port_id_to_security_groups = {}
        for port_use_security_group in r_port_use_security_group:
            security_group = next(
                (v for v in cloud_security_group if v['nativeId'] == port_use_security_group['sgNativeId']), None)
            vfw_port = next((p for p in cloud_port if p['id'] == port_use_security_group['portId']), None)
            if security_group and vfw_port:
                port_id = port_use_security_group['portId']
                if port_id not in port_id_to_security_groups:
                    port_id_to_security_groups[port_id] = []
                port_id_to_security_groups[port_id].append((security_group, vfw_port))

        # 构建port_security_list
        port_security_list = []
        for port_id, security_group_vfw_port_pairs in port_id_to_security_groups.items():
            for security_group, vfw_port in security_group_vfw_port_pairs:
                # 从预构建的映射中获取安全组规则
                if security_group.get("id") in security_group_id_to_rules:
                    for security_group_rule in security_group_id_to_rules[security_group.get("id")]:
                        vfw_port_dict = {
                            "enabled": vfw_port.get("status"),
                            "direction": security_group_rule.get("direction"),
                            "protocol": security_group_rule.get("protocol"),
                            "subnetIds": security_group_rule.get("subnetIds"),
                            "ruleType": security_group_rule.get("direction"),
                            "remoteIpPrefix": security_group_rule.get("remoteIpPrefix"),
                            "portRangeMax": security_group_rule.get("portRangeMax"),
                            "portRangeMin": security_group_rule.get("portRangeMin"),
                            "ipAddressType": security_group_rule.get("ethertype"),
                            "ruleId": security_group_rule.get("id"),
                            "portId": port_id
                        }
                        port_security_list.append(vfw_port_dict)

        # 优化：使用字典按portId分组port_security_list
        port_id_to_security_infos = {}
        for security_info in port_security_list:
            port_id = security_info.get("portId")
            if port_id not in port_id_to_security_infos:
                port_id_to_security_infos[port_id] = []
            port_id_to_security_infos[port_id].append(security_info)

        # 再将数据进行整合
        all_port_vfw_list = []
        for vfw_port_info in vfw_port_list:
            port_id = vfw_port_info.get("portId")
            # 使用字典快速查找匹配的安全组信息
            if port_id in port_id_to_security_infos:
                for security_info in port_id_to_security_infos[port_id]:
                    all_port_vfw_list.append(
                        {"related": {}, "_source_id": source_key, "primary": {**vfw_port_info, **security_info}})
        return all_port_vfw_list

    def get_cloud_model_data(self, firewall_policy_log_config, sources_list):
        """
            查询到这个模型所需要的所有的模型数据
        """
        cloud_data = {}
        cloud_side = firewall_policy_log_config.get("cloud_side")
        if not cloud_side:
            logger.warning(f"Related models not configured, skipping")
            return cloud_data
        # 通过这个数据源去查询数据  获取到数据源里的所有的数据
        for source in sources_list:
            client_key = f"cloud_{source}"
            if client_key not in self.api_clients:
                logger.warning(f"Source {source} not configured, skipping")
                continue
            # 先获取云内主模型数据
            cloud_data[source] = {}
            primary_model = cloud_side.get("primary_model")
            get_cloud_model_key = f"get_{primary_model}"
            self._get_endpoint_url(get_cloud_model_key, primary_model)
            data = self._fetch_cloud_data_with_params(get_cloud_model_key, firewall_policy_log_config, {}, source)
            if not data:
                continue
            cloud_data[source]["primary"] = data
            cloud_data[source]["related"] = {}
            # 查询关联模型的数据
            related_models = firewall_policy_log_config.get("cloud_side", {}).get("related_models")
            for related_model in related_models:
                relation_model_name = related_model.get('relation_model_name')
                get_relation_model_key = f"get_{relation_model_name}"
                self._get_endpoint_url(get_relation_model_key, relation_model_name)
                related_data = self._fetch_cloud_data_with_params(get_relation_model_key,
                                                                  firewall_policy_log_config, {}, source)
                if not related_data:
                    continue
                cloud_data[source]["related"][relation_model_name] = related_data
            # 查询关联关系的数据
            relation_tables = firewall_policy_log_config.get("cloud_side", {}).get("relation_tables")
            for relation_table in relation_tables:
                relation_table_name = relation_table.get("relation_table_name")
                get_relation_table_key = f"relation_{relation_table_name}"
                self._get_endpoint_url(get_relation_table_key, relation_table_name)
                related_table = self._fetch_cloud_data_with_params(get_relation_table_key,
                                                                   firewall_policy_log_config, {},
                                                                   source)

                cloud_data[source]["related"][relation_table_name] = related_table
        return cloud_data

    def get_application_by_eps_ids(self, esp_ids, client_key, model_config):
        """
        通过ESP ID列表批量获取应用信息，支持缓存机制

        Args:
            esp_ids: ESP ID列表
            client_key: 客户端密钥
            model_config: 模型配置

        Returns:
            包含所有ESP ID对应应用信息的字典
        """
        if not esp_ids:
            return {}

        # 读取或创建缓存文件
        file_path = self.cache_dir / 'applicationSystem.json'
        esp_data_info = self._read_or_create_json_file(file_path, {})

        # 分离已缓存和未缓存的ID
        cached_ids = {}
        missing_ids = []

        for esp_id in esp_ids:
            if esp_id in esp_data_info:
                cached_ids[esp_id] = esp_data_info[esp_id]
            else:
                missing_ids.append(esp_id)

        # 如果所有ID都已缓存，直接返回
        if not missing_ids:
            return cached_ids

        # 验证client_key
        if not client_key:
            return cached_ids  # 至少返回已缓存的数据

        # 构建SC客户端密钥
        sc_client_key = f"sc_cloud_{client_key}"
        self._ensure_authentication(sc_client_key)
        # 获取API配置
        client_config = self.api_clients.get(sc_client_key)
        if not client_config:
            logger.warning(f"Client config not found for {sc_client_key}")
            return cached_ids

        api_config = client_config.get('config', {})
        api_base_url = api_config.get('api_base_url')
        api_base_port = api_config.get('api_base_port')

        if not api_base_url or not api_base_port:
            logger.warning(f"Missing API base URL or port for {sc_client_key}")
            return cached_ids

        # 构建请求URL
        sc_eps_url = self.endpoints.get('cloud_side', {}).get('sc_eps_url', '')
        query_sc_eps_url = f"{api_base_url}:{api_base_port}{sc_eps_url}"

        # 准备请求会话
        session = client_config.get('session')
        if not session:
            logger.warning(f"Session not found for {sc_client_key}")
            return cached_ids

        session.verify = False

        # 调用API获取数据（批量查询或循环查询）
        new_application_info = {}

        try:
            # 假设API支持批量查询（根据实际API调整）
            # 这里简化为循环查询每个ID
            for esp_id in missing_ids:
                # 如果API不支持批量，需要在URL中包含esp_id
                # 假设API通过URL参数接收esp_id，实际需根据API调整
                batch_query_url = query_sc_eps_url.replace("{id}", esp_id)

                response = session.get(batch_query_url, timeout=30)
                response.raise_for_status()

                # 处理空响应
                if not response.text.strip():
                    logger.debug(f"Empty response for ESP ID: {esp_id}")
                    continue

                # 解析JSON响应
                resp_data = response.json()

                # 提取结果数据
                result_data = self._get_nested_value(resp_data, "enterprise_project")
                if not result_data:
                    logger.debug(f"Result data not found for ESP ID: {esp_id}")
                    continue

                # 处理应用信息
                application_info = self._process_application_info(result_data)
                if application_info:
                    new_application_info[esp_id] = application_info

        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
        except (KeyError, ValueError, TypeError) as e:
            logger.error(f"Data processing error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")

        # 更新缓存（仅添加新获取的数据，不覆盖已有缓存）
        if new_application_info:
            esp_data_info.update(new_application_info)
            self._write_json_file(file_path, esp_data_info)

        # 返回所有结果（已缓存的 + 新获取的）
        return {**cached_ids, **new_application_info}

    def _get_nested_value(self, data, path):
        """安全地获取嵌套字典中的值"""
        if not path:
            return data
        keys = path.split('.')
        for key in keys:
            if isinstance(data, dict) and key in data:
                data = data[key]
            else:
                return None
        return data

    def _process_application_info(self, result_data):
        """处理应用信息，根据name和description的不同规则提取并格式化必要字段"""
        return (result_data.get("name", "")[:6] or "").upper()

    def _write_json_file(self, file_path, data):
        """写入JSON数据到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to write cache file: {e}")

    def _read_or_create_json_file(self, file_path: str, default_content: dict = None) -> dict:
        """
        读取JSON文件内容，如果文件不存在则创建并写入默认内容

        Args:
            file_path: 文件路径
            default_content: 文件不存在时的默认内容（默认为空字典）

        Returns:
            文件中的JSON数据（字典或列表）
        """
        # 设置默认内容为空字典
        if default_content is None:
            default_content = {}

        path = Path(file_path)

        # 检查文件是否存在
        if not path.exists():
            # 创建文件并写入默认内容
            path.parent.mkdir(parents=True, exist_ok=True)
            path.write_text(json.dumps(default_content, ensure_ascii=False, indent=2), encoding="utf-8")
            logger.debug(f"文件 '{file_path}' 不存在，已创建并写入默认内容")
            return default_content

        # 读取并解析JSON内容
        try:
            content = path.read_text(encoding="utf-8").strip()

            # 处理空文件
            if not content:
                logger.debug(f"文件 '{file_path}' 为空，写入默认内容")
                path.write_text(json.dumps(default_content, ensure_ascii=False, indent=2), encoding="utf-8")
                return default_content

            return json.loads(content)

        except json.JSONDecodeError as e:
            logger.debug(f"JSON解析错误: {e}")
            # 解析失败时写入默认内容并返回
            path.write_text(json.dumps(default_content, ensure_ascii=False, indent=2), encoding="utf-8")
            return default_content
        except Exception as e:
            logger.debug(f"读取文件失败: {e}")
            return default_content

    @staticmethod
    def check_ip_version(ip_str):
        """
        判断输入的IP地址或网段的版本（IPv4/IPv6）

        参数:
            ip_str (str): 输入的IP字符串，可以是以下格式：
                - 标准IP地址 (*********** 或 2001:db8::1)
                - CIDR表示法 (***********/24 或 2001:db8::/32)
                - IP范围 (***********-100 或 ***********-***********00)
                - 带子网掩码的IPv4 (*********** *************)

        返回:
            str: "IPv4"、 "IPv6" 或 空字符串（无效输入）
        """
        # 处理空值或非字符串输入
        if not ip_str or not isinstance(ip_str, str):
            return ""

        # 清理输入：移除引号、括号和多余空格
        cleaned_ip = re.sub(r'[\'"()]', '', ip_str).strip()

        # 1. 检查CIDR格式 (包含'/')
        if '/' in cleaned_ip:
            try:
                network = ipaddress.ip_network(cleaned_ip, strict=False)
                return "IPv4" if network.version == 4 else "IPv6"
            except ValueError:
                return ""

        # 2. 检查IP范围格式 (包含'-')
        if '-' in cleaned_ip:
            parts = cleaned_ip.split('-', 1)
            start = parts[0].strip()
            end = parts[1].strip()

            # 处理IPv4简写范围 (如 ***********-100)
            if '.' in start and '.' not in end and end.isdigit():
                base = start.rsplit('.', 1)[0]
                end = f"{base}.{end}"

            try:
                start_addr = ipaddress.ip_address(start)
                end_addr = ipaddress.ip_address(end)
                if start_addr.version != end_addr.version:
                    return ""
                return "IPv4" if start_addr.version == 4 else "IPv6"
            except ValueError:
                return ""

        # 3. 检查带子网掩码的IPv4 (空格分隔)
        if ' ' in cleaned_ip:
            parts = cleaned_ip.split()
            if len(parts) == 2:
                ip_part, mask_part = parts[0], parts[1]

                # 验证IPv4地址部分
                try:
                    ipaddress.IPv4Address(ip_part)
                except ValueError:
                    return ""

                # 验证子网掩码格式
                try:
                    # 尝试直接解析为子网掩码
                    mask = ipaddress.IPv4Address(mask_part)
                    # 检查是否是有效掩码
                    mask_int = int(mask)
                    if mask_int == 0:
                        prefix_len = 0
                    else:
                        # 计算前缀长度
                        bin_str = bin(mask_int)[2:]
                        if '01' in bin_str or not bin_str.startswith('1' * bin_str.count('1')):
                            return ""
                        prefix_len = bin_str.count('1')

                    # 验证整个网络
                    ipaddress.IPv4Network(f"{ip_part}/{prefix_len}", strict=False)
                    return "IPv4"
                except (ipaddress.AddressValueError, ValueError):
                    return ""

        # 4. 检查标准IP地址
        try:
            addr = ipaddress.ip_address(cleaned_ip)
            return "IPv4" if addr.version == 4 else "IPv6"
        except ValueError:
            return ""

    def collect_sys_device_link(self, sync_record=None, force_refresh=False):
        """收集并同步系统设备连接数据"""
        # 获取配置并初始化同步记录
        sys_device_link_cfg = self.models.get('sys_deviceLink')
        if sync_record is None:
            sync_direction = self._determine_sync_direction(sys_device_link_cfg)
            sync_record = SyncRecord('sys_deviceLink', 'full', sync_direction)
            sync_record.source_id = sys_device_link_cfg.get('cloud_side', {}).get('source_id')
        if not sys_device_link_cfg.get("sync_enabled"):
            return sync_record
        # 获取数据源并检查
        sources_list = self._get_model_sources('sys_deviceLink')
        field_mappings = sys_device_link_cfg.get('field_mappings')

        if not sources_list:
            logger.info("No sources found for sys_deviceLink")
            return sync_record

        # 获取并缓存云端数据
        if "sys_deviceLink" in self.cloud_data and not force_refresh:
            cloud_data = self.cloud_data["sys_deviceLink"]
        else:
            cloud_data = self.get_cloud_model_data(sys_device_link_cfg, sources_list)
            if not cloud_data:
                logger.info("No cloud data to sync for sys_deviceLink")
                return sync_record
            self._save_to_cache("sys_deviceLink", cloud_data)

        # 获取行内关联数据并提取ID列表
        internal_data = self.fetch_all_internal_data(model_id="sys_deviceLink")
        sys_device_link_data = self.internal_related_data.get("sys_deviceLink", {})

        network_devices = sys_device_link_data.get("NetworkDevice", [])
        switch_ports = sys_device_link_data.get("SwitchPort", [])

        # 提取有效ID并转换为集合提高查询效率
        network_device_ids = {
            item["InstanceId"]
            for item in network_devices
            if isinstance(item, dict) and item.get("InstanceId") is not None
        }
        switch_port_ids = {
            item["InstanceId"]
            for item in switch_ports
            if isinstance(item, dict) and item.get("InstanceId") is not None
        }

        # 筛选有效的设备连接数据
        # 组装同步数据
        sync_data = []
        for source_key, source_data in cloud_data.items():
            primary_data = source_data.get("primary", [])
            eps_ids = [item["epsId"] for item in primary_data if "epsId" in item and item["epsId"] != "0"]
            application_dict = self.get_application_by_eps_ids(eps_ids, source_key, sys_device_link_cfg)
            self.application_code.update(application_dict)

            # 筛选有效的设备连接
            valid_device_links = [
                item for item in primary_data
                if item.get("idOfSrcDevice") in network_device_ids and
                   item.get("idOfDestDevice") in network_device_ids and
                   item.get("idOfSrcPort") in switch_port_ids and
                   item.get("idOfDestPort") in switch_port_ids
            ]

            if not valid_device_links:
                logger.info("No valid device links to sync for source: %s", source_key)
                continue

            # 获取关联模型数据并构建索引
            related_data = source_data.get("related", {})
            network_devices = related_data.get("SYS_NetworkDevice", [])
            network_ports = related_data.get("SYS_NetworkDevicePort", [])
            racks = related_data.get("SYS_Rack", [])
            rack_device_relations = related_data.get("M_RackContainsDevice", [])

            # 构建ID到对象的映射，避免重复遍历
            device_index = {item["id"]: item for item in network_devices}
            port_index = {item["id"]: item for item in network_ports}
            rack_relation_index = {rel["target_Instance_Id"]: rel for rel in rack_device_relations}
            rack_index = {rack["id"]: rack for rack in racks}

            for item in valid_device_links:
                src_device_id = item.get("idOfSrcDevice")
                dest_device_id = item.get("idOfDestDevice")
                src_port_id = item.get("idOfSrcPort")
                dest_port_id = item.get("idOfDestPort")

                # 获取设备信息
                src_device = device_index.get(src_device_id, {})
                dest_device = device_index.get(dest_device_id, {})

                # 获取端口信息
                src_port = port_index.get(src_port_id, {})
                dest_port = port_index.get(dest_port_id, {})

                # 获取机柜信息
                src_rack_info = self._get_rack_info(src_device_id, rack_relation_index, rack_index)
                dest_rack_info = self._get_rack_info(dest_device_id, rack_relation_index, rack_index)
                # 构建同步数据
                sync_item = {
                    "resId": item.get("resId"),
                    "class_Name": item.get("class_Name"),
                    "name": item.get("name"),
                    "notes": item.get("notes"),
                    "srcDeviceName": src_device.get("name"),
                    "srcDeviceId": src_device_id,
                    "srcDeviceRackName": src_rack_info.get("name"),
                    "srcDevicePortName": src_port.get("name"),
                    "srcDevicePortId": src_port_id,
                    "srcDevicePosition": src_rack_info.get("position"),
                    "targetDeviceName": dest_device.get("name"),
                    "targetDeviceId": dest_device_id,
                    "targetDeviceRackName": dest_rack_info.get("name"),
                    "targetDevicePortName": dest_port.get("name"),
                    "targetDevicePortId": dest_port_id,
                    "targetDevicePosition": dest_rack_info.get("position"),
                }
                sync_data.append({"related": {}, "_source_id": source_key, "primary": sync_item})

        if not sync_data:
            logger.info("No valid device links to sync after all sources processed")
            return sync_record

        # 同步数据并保存记录
        self._sync_cloud_to_internal(
            "sys_deviceLink",
            sys_device_link_cfg,
            sync_data,
            internal_data.get("sys_deviceLink"),
            field_mappings,
            sync_record
        )
        model_id = "sys_deviceLink"
        # 同步完成后，保存当前数据作为历史数据
        self.history_data[model_id] = {
            'cloud_data': self.cloud_data.get(model_id, {}),
            'internal_data': self.internal_data.get(model_id),
            'sync_data': self.sync_data.get(model_id, {}),
            'timestamp': time.time()
        }

        self._save_to_cache(f"history_{'sys_deviceLink'}", self.history_data['sys_deviceLink'], expire_time=86400)

        # 同步完成后更新记录状态
        sync_record.complete('success')

        self.record_manager.save_record(sync_record)
        return sync_record

    def _get_rack_info(self, device_id, rack_relation_index, rack_index):
        """获取设备的机柜信息"""
        rack_info = {"name": None, "position": None}
        device_rack_relation = rack_relation_index.get(device_id)

        if device_rack_relation:
            rack_id = device_rack_relation.get('source_Instance_Id')
            rack = rack_index.get(rack_id)
            if rack:
                rack_info["name"] = rack.get('name')
                rack_info["position"] = device_rack_relation.get("position")

        return rack_info

    def _count_items_in_request_data(self, request_data, batch_items=None):
        """统计请求数据中的实际条目数量（通用方法）

        Args:
            request_data: 请求数据，可能是嵌套结构或列表
            batch_items: 原始批次项目列表（可选，用作备用计数）

        Returns:
            实际的数据项数量
        """
        if isinstance(request_data, dict):
            # 如果是嵌套结构，需要深入查找实际的数据列表
            for key, value in request_data.items():
                if isinstance(value, dict):
                    # 继续深入查找
                    for sub_key, sub_value in value.items():
                        if isinstance(sub_value, list):
                            return len(sub_value)
                elif isinstance(value, list):
                    return len(value)
            # 如果找不到列表，返回字典的键数量
            return len(request_data)
        elif isinstance(request_data, list):
            # 如果是列表，直接返回长度
            return len(request_data)
        else:
            # 如果都不是，返回原始批次项目的数量
            return len(batch_items) if batch_items else 1

    def _add_failed_request_data_to_retry_collection(self, request_data, batch_items):
        """将失败的封装后请求数据添加到重试集合中

        Args:
            request_data: 封装后的请求数据，可能是嵌套结构或列表
            batch_items: 原始批次项目列表（用于日志记录）
        """
        if request_data:
            # 将封装后的请求数据添加到重试列表中
            self.retry_error_update_datas.append({
                'request_data': request_data,
                'original_count': len(batch_items) if batch_items else 0
            })

            # 计算当前总的失败条目数
            total_failed_items = self._count_total_failed_items()
            logger.debug(f"添加失败的封装数据到重试集合，本批次项目数: {len(batch_items) if batch_items else 0}，当前重试批次数: {len(self.retry_error_update_datas)}，累计失败条目数: {total_failed_items}")

    def _add_failed_delete_data_to_retry_collection(self, batch_items):
        """将失败的删除数据添加到重试集合中

        Args:
            batch_items: 原始批次项目列表
        """
        if batch_items:
            self.retry_error_delete_datas.extend(batch_items)
            logger.debug(f"添加 {len(batch_items)} 个失败删除项目到重试集合，当前总数: {len(self.retry_error_delete_datas)}")

    def _retry_failed_update_items_one_by_one(self, endpoint, internal_side_config, sync_record, operation_type="created"):
        """逐条重试失败的更新项目

        Args:
            endpoint: API端点
            internal_side_config: 内部配置
            sync_record: 同步记录
        """
        if not self.retry_error_update_datas:
            return

        total_failed_items = self._count_total_failed_items()
        logger.info(f"开始逐条重试 {len(self.retry_error_update_datas)} 个失败的批次，总计 {total_failed_items} 个条目")

        for batch_index, retry_batch in enumerate(self.retry_error_update_datas, 1):
            request_data = retry_batch['request_data']
            original_count = retry_batch['original_count']

            logger.info(f"重试第 {batch_index}/{len(self.retry_error_update_datas)} 个批次，包含 {original_count} 个项目")

            # 将批次数据拆分为单个项目进行重试
            single_items = self._extract_items_from_request_data(request_data)

            for single_item in single_items:
                # 重新封装单个项目为请求格式
                single_request_data = self._prepare_batch_internal_data(internal_side_config, [single_item])

                # 调用API进行单条更新
                response = self._call_internal_api(endpoint, {}, method='POST', data=single_request_data)

                # 使用统一的响应处理方法
                is_success, message = self._handle_api_response(response, "单条重试操作")

                if not is_success:
                    # 重试失败
                    if sync_record:
                        source_id = single_item.get('_source_id')
                        sync_record.add_failed(1, source_id)
                        sync_record.errors.append({
                            'error_type': 'sync_error',
                            'error_message': str({"msg":message,"data":single_request_data}),
                            'time': datetime.now(),
                            'record_id': None,
                            'stack_trace': None
                        })
                else:
                    # 重试成功
                    if sync_record:
                        # 判断操作类型并使用统一的成功计数方法（包含数据源统计）
                        source_id = single_item.get('_source_id')
                        sync_record.add_success(1, operation_type, source_id)
                #
                #         # 记录成功的变更
                #         primary_key = internal_side_config.get('primary_key')
                #         if primary_key and primary_key in single_item:
                #             record_id = single_item.get(primary_key)
                #             sync_record.add_change(record_id, "operation", None, operation_type)

        # 清空重试数据
        self.retry_error_update_datas.clear()
        logger.info("更新重试完成，清空错误数据集合")

    def _retry_failed_delete_items_one_by_one(self, endpoint, internal_side_config, sync_record):
        """逐条重试失败的删除项目

        Args:
            endpoint: API端点
            internal_side_config: 内部配置
            sync_record: 同步记录
        """
        if not self.retry_error_delete_datas:
            return

        logger.info(f"开始逐条重试删除 {len(self.retry_error_delete_datas)} 个项目")

        # 逐个重试删除项目
        for item in self.retry_error_delete_datas:
            # 将单个项目放入列表中，作为batch_size=1的批次
            single_item_batch = [item]

            # 调用原有的批量删除方法，但batch_size=1
            success = self._batch_delete_internal_items(
                endpoint,
                internal_side_config,
                single_item_batch,
                batch_size=1,
                sync_record=sync_record,
                retry=True
            )

            if success:
                logger.info(f"单条删除重试成功，项目ID: {item.get('id', 'unknown')}")
            else:
                logger.error(f"单条删除重试失败，项目ID: {item.get('id', 'unknown')}")

        # 清空重试数据
        self.retry_error_delete_datas.clear()
        logger.info("删除重试完成，清空错误删除数据集合")

    def _extract_items_from_request_data(self, request_data):
        """从封装的请求数据中提取原始项目列表

        Args:
            request_data: 封装后的请求数据

        Returns:
            原始项目列表
        """
        if isinstance(request_data, list):
            return request_data
        elif isinstance(request_data, dict):
            # 处理嵌套结构
            for key, value in request_data.items():
                if isinstance(value, dict):
                    for sub_key, sub_value in value.items():
                        if isinstance(sub_value, list):
                            return sub_value
                elif isinstance(value, list):
                    return value

        return []

    def _count_total_failed_items(self):
        """计算所有失败批次中的实际条目总数

        Returns:
            实际失败的条目总数
        """
        total_count = 0

        for retry_batch in self.retry_error_update_datas:
            request_data = retry_batch.get('request_data', {})
            original_count = retry_batch.get('original_count', 0)

            # 优先使用original_count，如果没有则使用通用计数方法
            if original_count > 0:
                total_count += original_count
            else:
                # 使用通用的计数方法
                total_count += self._count_items_in_request_data(request_data)

        return total_count

    def _finalize_sync_record_statistics(self, sync_record):
        """完成同步记录的统计并进行验证

        Args:
            sync_record: 同步记录对象
        """
        if not sync_record:
            return

        # 验证统计数据的一致性
        is_consistent = sync_record.validate_counts()

        # 获取统计摘要
        summary = sync_record.get_summary()

        # 记录统计摘要
        logger.info(f"同步统计摘要 - 模型: {sync_record.model_id}")
        logger.info(f"  总记录数: {summary['total_records']}")
        logger.info(f"  成功: {summary['success_count']} (创建: {summary['created_count']}, 更新: {summary['updated_count']}, 删除: {summary['deleted_count']})")
        logger.info(f"  失败: {summary['failed_count']}")
        logger.info(f"  跳过: {summary['skipped_count']}")
        logger.info(f"  成功率: {summary['success_rate']:.1f}%")
        logger.info(f"  统计一致性: {'PASS' if summary['is_consistent'] else 'FAIL'}")

        if not is_consistent:
            logger.warning("检测到统计数据不一致，请检查同步逻辑")

        # 设置同步状态
        if summary['failed_count'] == 0:
            sync_record.status = 'success'
        elif summary['success_count'] > 0:
            sync_record.status = 'partial_success'
        else:
            sync_record.status = 'failed'

    def _handle_api_response(self, response, operation_name="API操作", log_success=True):
        """统一处理API响应，避免空指针问题

        Args:
            response: API响应对象
            operation_name: 操作名称，用于日志记录
            log_success: 是否记录成功日志

        Returns:
            tuple: (is_success: bool, error_message: str)
        """
        if response is None:
            error_msg = f"{operation_name}失败: API调用返回None（可能是网络超时、服务不可用或认证失败）"
            logger.error(error_msg)
            return False, error_msg

        if 'ERROR' in response.get('msg', ''):
            error_msg = f"{operation_name}失败: {response.get('msg', '')}"
            logger.error(error_msg)
            return False, error_msg

        # 成功情况
        if log_success:
            success_msg = response.get('msg', '操作成功')
            logger.info(f"{operation_name}成功: 操作成功")

        return True, response.get('msg', '操作成功')

    def _safe_get_response_field(self, response, field, default=None):
        """安全获取响应字段，避免空指针异常

        Args:
            response: API响应对象
            field: 字段名
            default: 默认值

        Returns:
            字段值或默认值
        """
        if response is None:
            return default
        return response.get(field, default)

    def _prepare_sync_item(self, source_id, internal_key, previous_sync_item, cloud_update_item,
                           sync_record=None, model_config=None):
        """准备更新项目，检查是否有变更"""
        is_change = False
        backfill_field = model_config.get('backfill_field', [])
        internal_side_config = model_config.get('internal_side', {})
        update_params = internal_side_config.get('update_params', {})
        api_config = update_params.get('api_config', {})
        nested_config = api_config.get('nested_config', {})
        constant_value = nested_config.get('constant_value')
        internal_primary_key = internal_side_config.get('primary_key')
        updated_item = self._deep_compare_sync_keys(previous_sync_item, cloud_update_item, constant_value, internal_key, sync_record)

        if not updated_item and sync_record:
            logger.debug(f"Sync item changed: {is_change}")
            sync_record.add_skipped(1, source_id)
            return None, is_change
        else:
            is_change = True

        logger.debug(f"Sync item changed: {is_change}")

        # 字段回填
        self._handle_backfill_field(updated_item, backfill_field, internal_primary_key, internal_key)

        return updated_item, is_change

    def _deep_compare_sync_keys(self, old_dict, new_dict, class_key='Instance', internal_key=None, sync_record=None):
        """
        比较 new_dict 和 old_dict 中 key 的值是否发生变化，支持嵌套结构。
        如果存在不一致，返回包含不一致 key 和 new_dict 中对应值的字典，
        并附加与 ClassKey 值对应的字段（基于 new_dict）。
        如果 old_dict 中存在 new_dict 中没有的 key，则将其值设为 None 并输出。
        如传入 sync_record，则记录每个字段的变更。
        """

        def is_equal(v1, v2):
            if type(v1) != type(v2):
                return False
            if isinstance(v1, dict):
                return self._deep_compare_sync_keys(v1, v2) == {}
            elif isinstance(v1, list):
                return v1 == v2
            else:
                return v1 == v2

        result = {}

        # 遍历 new_dict 的所有 key，与 old_dict 做比较
        for key in new_dict:
            old_value = old_dict.get(key)
            new_value = new_dict[key]
            if key not in old_dict or not is_equal(old_value, new_value):
                result[key] = new_value
                logger.debug(f"Field {key} changed from {old_value} to {new_value}")
                # if sync_record:
                #     sync_record.add_change(internal_key, key, old_value, new_value)

        # 处理 old_dict 中多出的 key，设为 None
        # for key in old_dict:
        #     if key != '_source_id' and key not in new_dict:
        #         old_value = old_dict[key]
        #         result[key] = None
        #         logger.debug(f"Field {key} changed from {old_value} to None ")
        #         if sync_record:
        #             sync_record.add_change(internal_key, key, old_value, None)

        # 如果有不一致项，处理 ClassKey 相关逻辑
        if result:
            result[class_key] = new_dict.get(class_key)

        return result