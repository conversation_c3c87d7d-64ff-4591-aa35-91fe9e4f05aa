2025-08-26 09:46:04,246 - 21732-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-26 09:46:04,248 - 21732-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-26 09:46:04,249 - 21732-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-26 09:46:04,250 - 21732-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-26 09:46:04,251 - 21732-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-26 09:46:04,252 - 21732-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-26 09:46:04,378 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-26 09:46:04,465 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-26 09:46:04,471 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-26 09:46:04,472 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-26 09:46:04,491 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-26 09:46:04,806 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-26 09:46:04,913 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-26 09:46:05,350 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-26 09:46:05,517 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-26 09:46:05,652 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-26 09:46:05,800 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-26 09:46:06,073 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-26 09:46:06,247 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-26 09:46:06,395 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-26 09:46:06,583 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-26 09:46:06,770 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-26 09:46:06,965 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-26 09:46:07,224 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-26 09:46:07,459 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-26 09:46:07,574 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-26 09:46:07,917 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-26 09:46:07,988 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-26 09:46:08,066 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-26 09:46:08,326 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-26 09:46:08,428 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-26 09:46:08,714 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-26 09:46:08,824 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-26 09:46:08,960 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-26 09:46:09,229 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-26 09:46:09,486 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-26 09:46:09,676 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-26 09:46:09,919 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-26 09:46:10,049 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-26 09:46:10,244 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-26 09:46:10,383 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-26 09:46:10,579 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-26 09:46:10,749 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-26 09:46:10,962 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-26 09:46:11,312 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-26 09:46:11,664 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-26 09:46:11,942 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-26 09:46:12,263 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-26 09:46:12,499 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-26 09:46:12,721 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-26 09:46:12,805 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-26 09:46:12,904 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-26 09:46:13,059 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-26 09:46:13,286 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-26 09:46:13,383 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-26 09:46:13,509 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-26 09:46:13,649 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-26 09:46:13,707 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-26 09:46:13,811 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-26 09:46:14,113 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-26 09:46:14,400 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-26 09:46:14,498 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-26 09:46:14,582 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-26 09:46:14,649 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-26 09:46:14,723 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-26 09:46:14,754 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-26 09:46:14,822 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-26 09:46:14,891 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-26 09:46:14,978 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-26 09:46:15,238 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-26 09:46:15,607 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-26 09:46:15,607 - 21732-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-26 09:46:15,969 - 21732-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-26 09:46:15,976 - 21732-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-26 09:46:15,976 - 21732-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-26 09:46:15,977 - 21732-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-26 09:46:15,977 - 21732-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-26 09:46:15,977 - 21732-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-26 09:46:16,017 - 21732-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:356] - INFO: Sync record database tables initialized successfully
2025-08-26 09:46:16,049 - 21732-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:356] - INFO: Sync record database tables initialized successfully
2025-08-26 09:46:16,092 - 21732-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-26 09:46:16,094 - 21732-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=30����, ��ʼ=00:00
2025-08-26 09:46:16,095 - 21732-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30']
2025-08-26 09:46:16,096 - 21732-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-26 09:46:16,097 - 21732-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ30����ִ��һ�Σ���00:00��ʼ
2025-08-26 09:46:16,098 - 21732-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:30, 01:00, 01:30, 02:00, 02:30, 03:00, 03:30, 04:00, 04:30, 05:00, 05:30, 06:00, 06:30, 07:00, 07:30, 08:00, 08:30, 09:00, 09:30, 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00, 13:30, 14:00, 14:30, 15:00, 15:30, 16:00, 16:30, 17:00, 17:30, 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30, 23:00, 23:30
2025-08-26 09:46:16,098 - 21732-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 48 ��
2025-08-26 09:46:16,099 - 21732-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-26 10:00:00, ���뻹�� 824.00 �룬������...
2025-08-26 09:46:33,649 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:324] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-08-26 09:46:33,649 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:329] - INFO: Using specified data source: source_1
2025-08-26 09:46:33,650 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:361] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-08-26 09:46:33,650 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:378] - INFO: Fetching data for model CLOUD_VM_NOVA from source source_1
2025-08-26 09:46:34,993 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2356] - INFO: Password authentication successful for cloud_source_1
2025-08-26 09:46:38,222 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 3.23s, ״̬��: 200
2025-08-26 09:46:40,035 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 1.81s, ״̬��: 200
2025-08-26 09:46:43,187 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 3.15s, ״̬��: 200
2025-08-26 09:46:43,347 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.16s, ״̬��: 200
2025-08-26 09:46:43,348 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:266] - INFO: Processing related models for CLOUD_VM_NOVA from source source_1
2025-08-26 09:46:43,348 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:277] - INFO: Processing related model R_Volume_MountOn_VM using method relation_table
2025-08-26 09:46:43,348 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2855] - INFO: Fetching relation table data for model CLOUD_VM_NOVA, related model R_Volume_MountOn_VM from source source_1
2025-08-26 09:46:43,505 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.16s, ״̬��: 200
2025-08-26 09:46:43,638 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:46:43,794 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-26 09:46:43,893 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:46:44,007 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:46:44,110 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:46:44,204 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:46:44,298 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:46:44,436 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:46:44,575 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:46:44,685 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:46:44,788 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:46:44,918 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:46:45,022 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:46:45,122 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:46:45,225 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:46:45,340 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:46:45,446 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:46:45,544 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:46:45,689 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:46:45,787 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:46:45,912 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:46:46,020 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:46:46,147 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:46:46,272 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:46:46,414 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:46:46,543 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:46:46,679 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:46:46,782 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:46:46,895 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:46:47,007 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:46:47,126 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:46:47,225 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:46:47,371 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:46:47,488 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:46:47,610 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:46:47,736 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:46:47,899 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.16s, ״̬��: 200
2025-08-26 09:46:48,034 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:46:48,168 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:46:48,330 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.16s, ״̬��: 200
2025-08-26 09:46:48,477 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:46:48,667 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.19s, ״̬��: 200
2025-08-26 09:46:48,856 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.18s, ״̬��: 200
2025-08-26 09:46:49,008 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-26 09:46:49,198 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.18s, ״̬��: 200
2025-08-26 09:46:49,427 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.22s, ״̬��: 200
2025-08-26 09:46:49,675 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.24s, ״̬��: 200
2025-08-26 09:46:49,832 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-26 09:46:50,047 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:285] - INFO: Fetched 381 records for related model R_Volume_MountOn_VM
2025-08-26 09:46:50,048 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:277] - INFO: Processing related model M_VMUsesFlavor using method relation_table
2025-08-26 09:46:50,049 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2855] - INFO: Fetching relation table data for model CLOUD_VM_NOVA, related model M_VMUsesFlavor from source source_1
2025-08-26 09:46:50,213 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.16s, ״̬��: 200
2025-08-26 09:46:50,367 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-26 09:46:50,495 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:46:50,615 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:46:50,756 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:46:50,883 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:46:50,992 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:46:51,108 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:46:51,229 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:46:51,389 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:46:51,503 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:46:51,641 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:46:51,774 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:46:51,919 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:46:52,031 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:46:52,134 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:46:52,291 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.15s, ״̬��: 200
2025-08-26 09:46:52,465 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.17s, ״̬��: 200
2025-08-26 09:46:52,489 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:285] - INFO: Fetched 90 records for related model M_VMUsesFlavor
2025-08-26 09:46:52,489 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:411] - INFO: Completed fetching cloud-side data for model: CLOUD_VM_NOVA from source source_1 (found 317 records)
2025-08-26 09:46:52,493 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:420] - ERROR: Error fetching cloud-side data for model CLOUD_VM_NOVA: rawData illegal
2025-08-26 09:46:52,510 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:514] - INFO: Fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-26 09:46:52,511 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:541] - INFO: Fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-26 09:46:52,589 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2356] - INFO: Bearer authentication successful for internal_side
2025-08-26 09:46:52,641 - 21732-Thread-9 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: CLOUD_VM_NOVA
2025-08-26 09:46:52,651 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.06s, ״̬��: 200
2025-08-26 09:46:52,652 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:605] - INFO: Completed fetching internal-side data for model: CLOUD_VM_NOVA (found 1 records)
2025-08-26 09:46:52,658 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:616] - INFO: Completed fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-26 09:46:52,659 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:245] - INFO: ��ʼͬ��ģ��: CLOUD_VM_NOVA
2025-08-26 09:46:52,734 - 21732-Thread-10 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:46:52,736 - 21732-Thread-10 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:46:52,744 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.08s, ״̬��: 404
2025-08-26 09:46:52,745 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 1/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:46:52,746 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:46:57,757 - 21732-Thread-11 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:46:57,758 - 21732-Thread-11 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:46:57,761 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:46:57,761 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 2/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:46:57,763 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:47:02,770 - 21732-Thread-12 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:47:02,771 - 21732-Thread-12 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:47:02,773 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:47:02,774 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 3/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:47:02,775 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:47:07,809 - 21732-Thread-13 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:47:07,810 - 21732-Thread-13 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:47:07,816 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.04s, ״̬��: 404
2025-08-26 09:47:07,817 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 4/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:47:07,819 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2160] - ERROR: HTTP���󣬲�������: 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:47:07,820 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4754] - ERROR: ��ȡ�ڲ�����ʧ��: API���÷���None�����������糬ʱ�����񲻿��û���֤ʧ�ܣ�
2025-08-26 09:47:07,821 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:681] - INFO: Synchronizing model: CLOUD_VM_NOVA
2025-08-26 09:47:07,822 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:698] - INFO: No historical data found for model CLOUD_VM_NOVA, using current internal data
2025-08-26 09:47:07,823 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:790] - INFO: Processing data from source source_1 for model CLOUD_VM_NOVA
2025-08-26 09:47:07,823 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2753] - INFO: Assembling data for model CLOUD_VM_NOVA
2025-08-26 09:47:11,013 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:812] - INFO: Syncing from cloud to internal for model: CLOUD_VM_NOVA
2025-08-26 09:47:11,062 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 49C0566302E537E48F98A9380B9EFB6E not found in internal, will be created
2025-08-26 09:47:11,094 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 71365B02B48533C0A7C7A2B4D4398989 not found in internal, will be created
2025-08-26 09:47:11,124 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key F5F125D6F72B37FB95C453AABE2E5DCB not found in internal, will be created
2025-08-26 09:47:11,153 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 169A8C59DB9A3AC984843ADE7025FEFA not found in internal, will be created
2025-08-26 09:47:11,186 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 3A26FF5E5A2C35C5A320B23D5D19D13A not found in internal, will be created
2025-08-26 09:47:11,212 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 30CF38857FD936B4A5EB990082AD46BA not found in internal, will be created
2025-08-26 09:47:11,246 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 2488EBED485E352595C948586C3468F3 not found in internal, will be created
2025-08-26 09:47:11,280 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key F6AE11C39CFA30EBB22A48313FF75B4C not found in internal, will be created
2025-08-26 09:47:11,311 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8F9E90E8BCC7377DB9B528A35B1DB658 not found in internal, will be created
2025-08-26 09:47:11,344 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 4186694301683E1194072802AFB28E87 not found in internal, will be created
2025-08-26 09:47:11,378 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8B882D14B81A35B0B97BAAA05D92E457 not found in internal, will be created
2025-08-26 09:47:11,409 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 0145D432CFE93260976B1B102E8ACB2B not found in internal, will be created
2025-08-26 09:47:11,442 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 9FA534661A1439A1A7F0AA8049A0D4FF not found in internal, will be created
2025-08-26 09:47:11,476 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5117C2D0FC473E0CB01E2ECE3560D586 not found in internal, will be created
2025-08-26 09:47:11,511 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E9CF16B059EA36B3870A6B564F512FE9 not found in internal, will be created
2025-08-26 09:47:11,542 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E9C56E57EDD8368AA4D6C15ACE08219C not found in internal, will be created
2025-08-26 09:47:11,569 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 81E5827DDCC33EFFA3C409605528B86F not found in internal, will be created
2025-08-26 09:47:11,600 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 51EFE7AEFB4C37859F7FDAB270298E87 not found in internal, will be created
2025-08-26 09:47:11,629 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 3E2DFFD24A6938A89524DF50ABD97253 not found in internal, will be created
2025-08-26 09:47:11,652 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 473A4F15159A3216BDAD704F45AAA11C not found in internal, will be created
2025-08-26 09:47:11,678 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 88083B835127352394C91C138E0BDC20 not found in internal, will be created
2025-08-26 09:47:11,710 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 1B5DFE15359838C0B72F3C61936D5B9B not found in internal, will be created
2025-08-26 09:47:11,745 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 4D9150B68E00384B80A7C077E9AB4950 not found in internal, will be created
2025-08-26 09:47:11,778 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key AECBB2BE5A7937CBB2139776867495FE not found in internal, will be created
2025-08-26 09:47:11,806 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D954A92F714A32699E556BF0CD0F36A5 not found in internal, will be created
2025-08-26 09:47:11,833 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 2E4B847B67363B73869D11AC9C0C382C not found in internal, will be created
2025-08-26 09:47:11,867 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 7B31014B43AF36228DFB020D9D8CA2A2 not found in internal, will be created
2025-08-26 09:47:11,896 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key AB1BED3EB4EE332E8D1663AEEE66A4D4 not found in internal, will be created
2025-08-26 09:47:11,924 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 52487227E6563479B1DB39872F5E8060 not found in internal, will be created
2025-08-26 09:47:11,956 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key C648FDF9E9523D5485AD183AA90DA476 not found in internal, will be created
2025-08-26 09:47:11,983 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key CC2372A91C8031E69F25BFBCEBB6F8F8 not found in internal, will be created
2025-08-26 09:47:12,013 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 96498DC422F9325D8D6969E96A457996 not found in internal, will be created
2025-08-26 09:47:12,042 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5BD4EBC9A90331898A7F41ED7D76D1F1 not found in internal, will be created
2025-08-26 09:47:12,068 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5DC7025DB2FE3D01BFCB9D1C69CD746B not found in internal, will be created
2025-08-26 09:47:12,093 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 76F4BDFBB1E73125AAD2767B4DC9989D not found in internal, will be created
2025-08-26 09:47:12,127 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 1BF8A3FAF3913107845CC9E4159A260D not found in internal, will be created
2025-08-26 09:47:12,157 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D99A7454BD6D331381A7838B160212F0 not found in internal, will be created
2025-08-26 09:47:12,191 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key DDD74332E83230C4AAABE1BACA29EFD0 not found in internal, will be created
2025-08-26 09:47:12,218 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key AEC5F654EF7E36ED97C94D2A9B062FD8 not found in internal, will be created
2025-08-26 09:47:12,246 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 84C4A05BA4943D9B855A3D15FB3BA7E1 not found in internal, will be created
2025-08-26 09:47:12,274 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5F9907121174300EA1B6C5A6C1A0E09E not found in internal, will be created
2025-08-26 09:47:12,295 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 467DDC9EDD8D329C93EA89B2A9F52554 not found in internal, will be created
2025-08-26 09:47:12,318 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 066485518BF132539A029FE67D89F227 not found in internal, will be created
2025-08-26 09:47:12,342 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8301737F8B323CBBBEB0EBC8785B0BAE not found in internal, will be created
2025-08-26 09:47:12,363 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E1AA3959B43D34BBA5DB26D80A2F4E44 not found in internal, will be created
2025-08-26 09:47:12,389 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 35B9B382CDD3335085B73585EEC2D179 not found in internal, will be created
2025-08-26 09:47:12,414 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key CBC92DB974AD305597795820F3ADCD64 not found in internal, will be created
2025-08-26 09:47:12,442 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key EECCB4D8253C3ADD9B2CE17FD39051B2 not found in internal, will be created
2025-08-26 09:47:12,462 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key CAA1C40D320F3CCFB3E160A2CAB20799 not found in internal, will be created
2025-08-26 09:47:12,481 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 44F44BDF89CE3B1497D678FCB25D1292 not found in internal, will be created
2025-08-26 09:47:12,500 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key C4362D25184B30AC80E68F872442F913 not found in internal, will be created
2025-08-26 09:47:12,520 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 71C4A1A3BCD83514AD69C4FD96399B8A not found in internal, will be created
2025-08-26 09:47:12,540 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8A1649E382C13815845BD4C72EF9602A not found in internal, will be created
2025-08-26 09:47:12,560 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 918C1120D96E35C09551304419EE9F60 not found in internal, will be created
2025-08-26 09:47:12,578 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 651501254E51357E9B64FBEE6983A5D0 not found in internal, will be created
2025-08-26 09:47:12,598 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E11AC75F5F8A396B81C6BB686ADD11E0 not found in internal, will be created
2025-08-26 09:47:12,618 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key BD9C01E08D9E3453A799C8F404541FAC not found in internal, will be created
2025-08-26 09:47:12,644 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 4B51938626B03C32AFD6AC0393EF42B0 not found in internal, will be created
2025-08-26 09:47:12,671 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key CC70449C4D5A3698855379531C5C4FC8 not found in internal, will be created
2025-08-26 09:47:12,696 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 2F160EE71C293B5D82F2B1E621B77643 not found in internal, will be created
2025-08-26 09:47:12,719 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 4B0A910BDB523F3693D09676E8A2F854 not found in internal, will be created
2025-08-26 09:47:12,737 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8E70849E039A3DBAAD1C8DDE2783D9CB not found in internal, will be created
2025-08-26 09:47:12,754 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 4E778F3D6DA238F4BE06F41966043B4B not found in internal, will be created
2025-08-26 09:47:12,777 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key C0978C2C1B423FA89A3E462796CA26B3 not found in internal, will be created
2025-08-26 09:47:12,797 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key FEA9322A9F9A37DD8A658B0082E3CB92 not found in internal, will be created
2025-08-26 09:47:12,827 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5F681AB62A0C37AD8B1E77CA72FEC6E2 not found in internal, will be created
2025-08-26 09:47:12,857 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key F203264C530839C9ADCA004314639967 not found in internal, will be created
2025-08-26 09:47:12,880 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5A94DDE198573180B28F38F8691CAC00 not found in internal, will be created
2025-08-26 09:47:12,902 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 4E430F5D768D39C2909A1C4C80999B0A not found in internal, will be created
2025-08-26 09:47:12,927 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 1FDCB48CAAB43EF9A2B46AF0BE9A18B8 not found in internal, will be created
2025-08-26 09:47:12,945 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key DB145CC9D4283EA19C0B94E9EE80E312 not found in internal, will be created
2025-08-26 09:47:12,966 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 9C28CB7559583EBAB1A52C5800B64FE5 not found in internal, will be created
2025-08-26 09:47:12,982 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 798ECA7BF090391984F814ED2AEB1F9F not found in internal, will be created
2025-08-26 09:47:13,002 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key AE865F280B99361DB2E070F7D2481541 not found in internal, will be created
2025-08-26 09:47:13,019 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key ED1FB39B7A9338D3A6888DA700892DF3 not found in internal, will be created
2025-08-26 09:47:13,039 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E34F9F8C70CD3061B174753733607C82 not found in internal, will be created
2025-08-26 09:47:13,061 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8436651BFCA031489A111DAE0650D60E not found in internal, will be created
2025-08-26 09:47:13,083 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 95BE313D782630B2AEDC3A9B4A9BDE9C not found in internal, will be created
2025-08-26 09:47:13,101 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 82A0743883A134B2B2E3293A2249EF90 not found in internal, will be created
2025-08-26 09:47:13,119 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key DDCF8CBAF86D3F3386BB50B5A1DB1DEA not found in internal, will be created
2025-08-26 09:47:13,137 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 9CF4BD083B9B3E31A995E380BF44094A not found in internal, will be created
2025-08-26 09:47:13,157 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key BF3B7DB9B680352491C8BF8D65AF35C8 not found in internal, will be created
2025-08-26 09:47:13,174 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 239895A19A55339A9BC5E17D9E59F7AD not found in internal, will be created
2025-08-26 09:47:13,195 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 78937EBCD1393A998DE9F7B321066D6C not found in internal, will be created
2025-08-26 09:47:13,213 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 1E32F6A9569A3C3C99E6672A0D08CA1B not found in internal, will be created
2025-08-26 09:47:13,230 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key EB69501CF3B73AFCB4F2AABD1790ED32 not found in internal, will be created
2025-08-26 09:47:13,247 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 7B1F70BA86AA3D20B2A5DC1D71F2CB95 not found in internal, will be created
2025-08-26 09:47:13,265 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D413488902613855B18D8AD44FA486E9 not found in internal, will be created
2025-08-26 09:47:13,284 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key B7EA332826BE30F7910FC47D27F311E1 not found in internal, will be created
2025-08-26 09:47:13,302 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8E88DC81C76D3546BE2A7226D89E749D not found in internal, will be created
2025-08-26 09:47:13,317 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 938175A7770938FE9F39355F6BC45DAD not found in internal, will be created
2025-08-26 09:47:13,333 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5E8CD9CE44C238AE884EEB6C53FC46B6 not found in internal, will be created
2025-08-26 09:47:13,348 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 4D1705747DA03A5FA11FE0A16379E7AD not found in internal, will be created
2025-08-26 09:47:13,364 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 365507914FAE357A82ED7AEE333672E4 not found in internal, will be created
2025-08-26 09:47:13,380 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 979C82F04EEE3E3D95EE552A2E0372E1 not found in internal, will be created
2025-08-26 09:47:13,395 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key EDC78DF7B847397CB48E8DE07FB7A608 not found in internal, will be created
2025-08-26 09:47:13,410 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 77B4298F40283333AF1058400AD308A8 not found in internal, will be created
2025-08-26 09:47:13,426 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 51D945869308326EA4F3B1FAD08764D7 not found in internal, will be created
2025-08-26 09:47:13,443 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key BCCCA55E780D374F9D09938BB360494A not found in internal, will be created
2025-08-26 09:47:13,461 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8CE8FD06F69F3291AACAD1D0BD97A27F not found in internal, will be created
2025-08-26 09:47:13,478 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key EAE1DAFF84B93C3B82BD05BC87CC12A8 not found in internal, will be created
2025-08-26 09:47:13,497 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key C21F7CAF365934889C151300221E4C94 not found in internal, will be created
2025-08-26 09:47:13,514 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 2FCFC121E1D236BF9B5DC9018FF6B2ED not found in internal, will be created
2025-08-26 09:47:13,530 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 3BE989A53B763D89872E660E1C51DEDE not found in internal, will be created
2025-08-26 09:47:13,545 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 95E3E9C0F5153B40ABEEA32299956684 not found in internal, will be created
2025-08-26 09:47:13,560 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D2EEEDB80F5B31EBB00092041F461015 not found in internal, will be created
2025-08-26 09:47:13,574 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 57C04B233F293AA6BC50EDCC6522F017 not found in internal, will be created
2025-08-26 09:47:13,589 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D2B578FB98ED3F189B878FA7DCA787BB not found in internal, will be created
2025-08-26 09:47:13,607 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 9719A5AB1914300396F903022FB16629 not found in internal, will be created
2025-08-26 09:47:13,621 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key EB132DF8CCC73F03861C02C597E79795 not found in internal, will be created
2025-08-26 09:47:13,644 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 6F7E8E23BB7B39D7970ED61ED7E3A0C8 not found in internal, will be created
2025-08-26 09:47:13,666 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D7B4746559F23E429329C8365E126E84 not found in internal, will be created
2025-08-26 09:47:13,690 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 2B9584DBA96D3DB4875268A5F26DE3D0 not found in internal, will be created
2025-08-26 09:47:13,716 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E28AE3EE7C3430E9B99B38FCCC7F2C0C not found in internal, will be created
2025-08-26 09:47:13,735 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key C5313B9BDA233F83A65341D677C2FE6F not found in internal, will be created
2025-08-26 09:47:13,752 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 72ABC544E42A347DB47C9A0F191D5FAC not found in internal, will be created
2025-08-26 09:47:13,768 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5AEE576FC8313CCCB83019CEA0F9F08A not found in internal, will be created
2025-08-26 09:47:13,783 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 18DBC719101F3AA98082A80D60CD8172 not found in internal, will be created
2025-08-26 09:47:13,803 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key DE7752F36A673366896D4BB5228FA7E7 not found in internal, will be created
2025-08-26 09:47:13,818 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 24541B6D09ED3A7DB64CF13CD0CF08CC not found in internal, will be created
2025-08-26 09:47:13,838 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 781336B912C63439B60399C329FBE8C6 not found in internal, will be created
2025-08-26 09:47:13,855 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D168F56B3B4B3240BB166D407C97A442 not found in internal, will be created
2025-08-26 09:47:13,873 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 575014F3FB8B3D7A9A8A16ABF84B48BE not found in internal, will be created
2025-08-26 09:47:13,891 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5656ACA6E52C32F798A9C961721C327C not found in internal, will be created
2025-08-26 09:47:13,908 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 37259214196B3BADA21A64CC485D668E not found in internal, will be created
2025-08-26 09:47:13,926 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 36E900A3A57B360A9256BBC9B945308B not found in internal, will be created
2025-08-26 09:47:13,946 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key A62AA2D54FCE39909F193CD6848AF39A not found in internal, will be created
2025-08-26 09:47:13,962 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5F88A67674C93B9D80038136375DF5F0 not found in internal, will be created
2025-08-26 09:47:13,978 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 9DAC9AA1A6A830B8A1044A7B1A5EED6D not found in internal, will be created
2025-08-26 09:47:13,995 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8E800DBF6538354EBF9B6879A156E6D2 not found in internal, will be created
2025-08-26 09:47:14,012 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key C388E90783C13AC9AB03C2ED04945616 not found in internal, will be created
2025-08-26 09:47:14,026 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 058C53D64E0E37BFADABA8CA18D67725 not found in internal, will be created
2025-08-26 09:47:14,040 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D9E1E9FD40223C59A22C266831479420 not found in internal, will be created
2025-08-26 09:47:14,056 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key C184CCF6B4CB34598E1CF504497767B1 not found in internal, will be created
2025-08-26 09:47:14,076 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5EEB14636CB832F9912A48011F93659C not found in internal, will be created
2025-08-26 09:47:14,095 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 760EC170C5073ABC85C2DCD80588C6F9 not found in internal, will be created
2025-08-26 09:47:14,115 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 268DE6AB989530CEA99A5C443D7651EF not found in internal, will be created
2025-08-26 09:47:14,132 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 403B7D8144223F16937B4541FFDA1828 not found in internal, will be created
2025-08-26 09:47:14,151 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key FDCBFBEAEBF63BBBB351E0830BD8D20B not found in internal, will be created
2025-08-26 09:47:14,172 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 7DEF26D95CDB334DBF3319C1D6F1BBF7 not found in internal, will be created
2025-08-26 09:47:14,189 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 4DCF0FB54849308796DA30D86FF3A338 not found in internal, will be created
2025-08-26 09:47:14,207 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 1D944E9838B4369D8F830918D313A8D6 not found in internal, will be created
2025-08-26 09:47:14,223 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key FBB9EB25370234998FE4DE18CB08C627 not found in internal, will be created
2025-08-26 09:47:14,241 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 20F86CDEFAA23242A67625B13BC5D896 not found in internal, will be created
2025-08-26 09:47:14,257 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 26394FFD75E73FCBAA2C6E8417BD4A2B not found in internal, will be created
2025-08-26 09:47:14,274 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 0192219BA34A34DC95561D71D73DBC8E not found in internal, will be created
2025-08-26 09:47:14,294 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 6CAB4D65732C3A64A83C60A6020D9521 not found in internal, will be created
2025-08-26 09:47:14,312 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key DE27B760EBEE3ABEBB9D7EF29F52A268 not found in internal, will be created
2025-08-26 09:47:14,336 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 6BA2EC9750043E91ADE47D3655AE9B90 not found in internal, will be created
2025-08-26 09:47:14,358 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key AC8B440F34C13C8DA574B274C90171D0 not found in internal, will be created
2025-08-26 09:47:14,379 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 1F25F8CA6E163EB18AA975F104C0CAE1 not found in internal, will be created
2025-08-26 09:47:14,395 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 441825D49BE7361491E3620921AA117F not found in internal, will be created
2025-08-26 09:47:14,412 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 54A2F5E98DA93E68AC477D3454AEA648 not found in internal, will be created
2025-08-26 09:47:14,430 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8A2982C051D435CEA76B1FD19E095FF6 not found in internal, will be created
2025-08-26 09:47:14,452 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 9CD46DD72DCA30F2BFB25746824484D7 not found in internal, will be created
2025-08-26 09:47:14,470 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 3A809A4BB4393852A8DFE0E55AC3FCEC not found in internal, will be created
2025-08-26 09:47:14,490 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 82C73B447A5733DB93992DA3FA8D44B6 not found in internal, will be created
2025-08-26 09:47:14,513 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 2BDF2375513C3098A9BB080887526C99 not found in internal, will be created
2025-08-26 09:47:14,532 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key EC26C0BB92803E12BC98582621CA612B not found in internal, will be created
2025-08-26 09:47:14,552 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 70AFFAE9D4DF309FA5B145DB1E427825 not found in internal, will be created
2025-08-26 09:47:14,571 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5E2548240AB134D5B3955D7615C3E289 not found in internal, will be created
2025-08-26 09:47:14,590 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D1DE5E7D3ED83B4B8A86FF88E74683A0 not found in internal, will be created
2025-08-26 09:47:14,611 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 240A6464447A3D7AAB858E7EBFC2AD90 not found in internal, will be created
2025-08-26 09:47:14,628 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 2CBB7013659E3394A1AB55B312EE99A8 not found in internal, will be created
2025-08-26 09:47:14,644 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key C1AFA9CCC2023B1C8B915ABE82AAD4A9 not found in internal, will be created
2025-08-26 09:47:14,663 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 89859B24F76836F88DE268D248A43B3A not found in internal, will be created
2025-08-26 09:47:14,679 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key DEA951D6F1493EF8AA2FCAF1ABEC666A not found in internal, will be created
2025-08-26 09:47:14,695 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 813EA37346F634C4AF0ED16DC5325E8E not found in internal, will be created
2025-08-26 09:47:14,715 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key F750C455365039EBBE1B83BF06D43BC7 not found in internal, will be created
2025-08-26 09:47:14,733 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5B433F11FC9D3DFA9195DABF18AC84BA not found in internal, will be created
2025-08-26 09:47:14,749 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 6D1E9BB2718F359EA0D62CB9286F80B6 not found in internal, will be created
2025-08-26 09:47:14,770 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key F5E852CB2AFB3EC18ED56B0BD3C93B31 not found in internal, will be created
2025-08-26 09:47:14,788 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 53806D65098A37D98BCF763DD5DB6BBC not found in internal, will be created
2025-08-26 09:47:14,805 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8A32C3922C943894B4887E3668357BE6 not found in internal, will be created
2025-08-26 09:47:14,821 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key B4CDF5298A603CFEB6776ECC5E710012 not found in internal, will be created
2025-08-26 09:47:14,838 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D79F606E7EEA349C84AA1AAFB29720D2 not found in internal, will be created
2025-08-26 09:47:14,854 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key F109377E3CE934B7B9BC70FB9690C43F not found in internal, will be created
2025-08-26 09:47:14,870 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 1769CAA314B6361DB3F7146CDCC60C93 not found in internal, will be created
2025-08-26 09:47:14,889 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 901BD3D07AD33634A15D8D1ABDC5F64C not found in internal, will be created
2025-08-26 09:47:14,910 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key BBCD76D5CFE939698CAFAE9F5C11B0A0 not found in internal, will be created
2025-08-26 09:47:14,930 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 4DB75C7D082839EA89630C553F71656B not found in internal, will be created
2025-08-26 09:47:14,949 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key C8F0DB256087306C8BA409FF58BDE415 not found in internal, will be created
2025-08-26 09:47:14,969 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 37D6FBB1DF3135AB8404B00E9D777741 not found in internal, will be created
2025-08-26 09:47:14,988 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 2D76629733153B55AF410794D2915FA7 not found in internal, will be created
2025-08-26 09:47:15,007 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 358056EABC4A37F8A6A580284FFCF7FE not found in internal, will be created
2025-08-26 09:47:15,024 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 7EE0218249013728978D80C043EB1265 not found in internal, will be created
2025-08-26 09:47:15,039 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 31E59346CB303AF8898B3D3CAEC9C18E not found in internal, will be created
2025-08-26 09:47:15,054 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 7BD85FBC805233B093B3F6E93433DBA6 not found in internal, will be created
2025-08-26 09:47:15,078 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key A57D9DAD33493BF195B9CFBD37AE0390 not found in internal, will be created
2025-08-26 09:47:15,096 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key F4BC28EC73F937789EA56018346FBA7B not found in internal, will be created
2025-08-26 09:47:15,113 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 69529796943B3C858E99CB083456F873 not found in internal, will be created
2025-08-26 09:47:15,130 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 70A9F584E6053F28BCAAF86B18CBFD06 not found in internal, will be created
2025-08-26 09:47:15,150 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 3C86E92223EB37A29F4F0940E603FFEE not found in internal, will be created
2025-08-26 09:47:15,172 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 58F802D15D6131C1AF2330A6BB0D6615 not found in internal, will be created
2025-08-26 09:47:15,189 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 46AE5D1BB6FF3EA897AB67DD83933586 not found in internal, will be created
2025-08-26 09:47:15,207 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key A3CCD3798091339492F4F4BF43E8BDBE not found in internal, will be created
2025-08-26 09:47:15,225 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key B64E68C1084C308A9C42514A52E954A0 not found in internal, will be created
2025-08-26 09:47:15,240 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5FE3C18C017B377EAEDF600578A7BEEE not found in internal, will be created
2025-08-26 09:47:15,255 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 9FDBFB27FFBF3B2EBDE6E7CBF74A7914 not found in internal, will be created
2025-08-26 09:47:15,273 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E3488E03EA393754B1977765C0C08A23 not found in internal, will be created
2025-08-26 09:47:15,290 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 841909E63B5036B3871BB4BEB53B934B not found in internal, will be created
2025-08-26 09:47:15,306 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key A801634F2E293634AE5FA0F6AD640E08 not found in internal, will be created
2025-08-26 09:47:15,323 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 86CD2D6416EF3509AC9A94753A3BA30B not found in internal, will be created
2025-08-26 09:47:15,340 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5565975BA86D357A912602AD5A368140 not found in internal, will be created
2025-08-26 09:47:15,364 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key C771E62CEB543A6B89274E3D4C41641F not found in internal, will be created
2025-08-26 09:47:15,385 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key AA45A520DDD63F9BB8B8281F95880A09 not found in internal, will be created
2025-08-26 09:47:15,408 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 45246009668C324F82686BAD360096A8 not found in internal, will be created
2025-08-26 09:47:15,425 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key EBA99564D4E037808921431D830B79D0 not found in internal, will be created
2025-08-26 09:47:15,442 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 0D1A99150A713D55BB0B53878EE48CF0 not found in internal, will be created
2025-08-26 09:47:15,458 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key F2EE3374CDF43C668083A81860BA4E33 not found in internal, will be created
2025-08-26 09:47:15,473 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 7F1A086DC7B13E90851FB22ECA7D2B7E not found in internal, will be created
2025-08-26 09:47:15,489 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 332E1A48A4A1342A9A77016317251867 not found in internal, will be created
2025-08-26 09:47:15,507 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 061201C58A0239AEBE029ACF50ED172D not found in internal, will be created
2025-08-26 09:47:15,522 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 7E40C95A8BA83C429AD7635B8951A6A4 not found in internal, will be created
2025-08-26 09:47:15,538 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D98CEACC160A34C19AB8FD3AEDD25EF6 not found in internal, will be created
2025-08-26 09:47:15,554 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 31956DE7D5CF34EA8CB1B7E3A3647205 not found in internal, will be created
2025-08-26 09:47:15,574 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 149AC57C62A63622AEA1707C4FA68CC5 not found in internal, will be created
2025-08-26 09:47:15,592 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 7FEF591B80173475B5308748DE1AE500 not found in internal, will be created
2025-08-26 09:47:15,613 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 2F7AED3B9BB53292ACD3CB8FEE4C35B2 not found in internal, will be created
2025-08-26 09:47:15,629 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 3EF96E938F3F3A389E74A88342FFDD06 not found in internal, will be created
2025-08-26 09:47:15,644 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key AB88ADA5BCDE3C77B51627AAC3B275DD not found in internal, will be created
2025-08-26 09:47:15,659 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 19BB615237CB39F995B4FC8F87A56E7F not found in internal, will be created
2025-08-26 09:47:15,674 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 81EB7C9982813DC9B10A30A8E4AC7412 not found in internal, will be created
2025-08-26 09:47:15,690 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key DF6B6286ABA331D5A2D8CD65E78E05E7 not found in internal, will be created
2025-08-26 09:47:15,705 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 604FC1D716683735A82CA5C4F072C655 not found in internal, will be created
2025-08-26 09:47:15,723 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 53D76ABCD7473D488ADE20A8C5F84484 not found in internal, will be created
2025-08-26 09:47:15,742 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key EDC9B57ECA9C374B8D4E8E5A3380EA1B not found in internal, will be created
2025-08-26 09:47:15,758 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 68BA38B53F9D32C1AB5FF0A7EA8349C0 not found in internal, will be created
2025-08-26 09:47:15,774 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 60BC1747849D3BA2BCCC49095D10E36A not found in internal, will be created
2025-08-26 09:47:15,792 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 667B03FAD59D3F4E95F357E804DB2781 not found in internal, will be created
2025-08-26 09:47:15,809 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key FFD226A1F3C43FEBA510445C92020F3E not found in internal, will be created
2025-08-26 09:47:15,827 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 0068BC2B44EE33409C6602DD6014D650 not found in internal, will be created
2025-08-26 09:47:15,846 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 78006A334111329C83DC700E6ED69E16 not found in internal, will be created
2025-08-26 09:47:15,861 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key FF12880806863E049F97DFB7A27E0A9C not found in internal, will be created
2025-08-26 09:47:15,877 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key DB94514846883473BF4AEB650919D6CE not found in internal, will be created
2025-08-26 09:47:15,893 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 3FD7E8C97F623C8C9527B3DDB73EDC31 not found in internal, will be created
2025-08-26 09:47:15,911 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 701228C3918E3F28A9E3FFC861CD0D2C not found in internal, will be created
2025-08-26 09:47:15,926 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D753E2F1254B35B889C5CEB9294C1354 not found in internal, will be created
2025-08-26 09:47:15,940 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E1A1E58E073738C2B79C0AC9796AE9B0 not found in internal, will be created
2025-08-26 09:47:15,957 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 0D58EAA34B9134DFBB32C85A36634903 not found in internal, will be created
2025-08-26 09:47:15,975 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key A2B390F4EF69314A869CE4B2361246C6 not found in internal, will be created
2025-08-26 09:47:15,990 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 2C4A2B7A593537F1A444940ED5E0B156 not found in internal, will be created
2025-08-26 09:47:16,009 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 3CE422D89C763DD7B3F62B609D6904A5 not found in internal, will be created
2025-08-26 09:47:16,026 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 9BBD2159643B36AAB11FA9D7F6CD7475 not found in internal, will be created
2025-08-26 09:47:16,042 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 65B8BDF808423D0E9A93F42F4C9289BC not found in internal, will be created
2025-08-26 09:47:16,060 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key B5DDC64CB54C38C4A49D1F3A657C7F50 not found in internal, will be created
2025-08-26 09:47:16,078 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 4B61F23C18A93E98B1DFF6CE6BF5874A not found in internal, will be created
2025-08-26 09:47:16,093 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key A290EA0C37533200AA28A833E704DBD6 not found in internal, will be created
2025-08-26 09:47:16,111 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 47E2F5EED46734C3A9A7164AD87D3683 not found in internal, will be created
2025-08-26 09:47:16,126 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key DED6C8AC4E633630996CEB2DCD6106EC not found in internal, will be created
2025-08-26 09:47:16,141 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 7946BFB836123C6D884AB47645673DC0 not found in internal, will be created
2025-08-26 09:47:16,156 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key DC7FE77AE8C638E392D52792C31A9AC0 not found in internal, will be created
2025-08-26 09:47:16,173 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 6C07FFAC3D063EA08669A38A027AF120 not found in internal, will be created
2025-08-26 09:47:16,192 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 18B75AD5AF9D36039B7575BA3F86FD03 not found in internal, will be created
2025-08-26 09:47:16,210 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 68BDBB7859343C0F9DEDCA5E4EB3DAA5 not found in internal, will be created
2025-08-26 09:47:16,228 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 7C4E0FE87A113A789F4AA1ACE7FDE3E2 not found in internal, will be created
2025-08-26 09:47:16,243 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 73D2E9226CE5340998F2182B1B0AF5A8 not found in internal, will be created
2025-08-26 09:47:16,260 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 6C7F53E163FD31E89EDAC87B15BDDC77 not found in internal, will be created
2025-08-26 09:47:16,276 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 83FC930B52CA3E7C95232F8D30122B66 not found in internal, will be created
2025-08-26 09:47:16,291 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 3E89379DCC4D3F81974F903DF7F31636 not found in internal, will be created
2025-08-26 09:47:16,311 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key B29C3ADADD99364FBB6409F9CD51A24E not found in internal, will be created
2025-08-26 09:47:16,326 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 73BA84E1AD173BC5A3072F32ECDBDDE7 not found in internal, will be created
2025-08-26 09:47:16,340 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D7953ADD820E38FF98537AA6253F70FC not found in internal, will be created
2025-08-26 09:47:16,359 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 1DCDBA6DA6ED34D79EC68B2D1158932B not found in internal, will be created
2025-08-26 09:47:16,374 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 9945084D33BA3D48947B2F9377EE8449 not found in internal, will be created
2025-08-26 09:47:16,390 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key A5EFDD5587363D1CBCD2783AE808084C not found in internal, will be created
2025-08-26 09:47:16,406 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key CA62453BA5093DE79C78D18B1CB3FEA5 not found in internal, will be created
2025-08-26 09:47:16,423 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E65CC7FDFE5F3298B89264C609F85AAD not found in internal, will be created
2025-08-26 09:47:16,441 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 91194AECB89A3CA19EB7EA44B88930DD not found in internal, will be created
2025-08-26 09:47:16,461 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 89570033C35139D4A4766F3520A416C8 not found in internal, will be created
2025-08-26 09:47:16,477 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 38FB68675236392083DE76804834988E not found in internal, will be created
2025-08-26 09:47:16,493 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key FDE1FC1D76BC3A05BBC244404A222FC5 not found in internal, will be created
2025-08-26 09:47:16,508 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key D198B899BAEA330FABF3530288FD2B87 not found in internal, will be created
2025-08-26 09:47:16,522 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key EB6D515D718530F1A1BE6A08CF49A253 not found in internal, will be created
2025-08-26 09:47:16,536 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E6980750D90E3B99BE284C559910868D not found in internal, will be created
2025-08-26 09:47:16,550 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8E4C6133EF8A32B48862E156BB0645C8 not found in internal, will be created
2025-08-26 09:47:16,565 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 308B993FB5EE3B4D942254C6A450F0DF not found in internal, will be created
2025-08-26 09:47:16,578 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 918E4743213D38AF8F29BF7CF59C5245 not found in internal, will be created
2025-08-26 09:47:16,591 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key AF068CECF68C3A65A613E7C8AB069A7F not found in internal, will be created
2025-08-26 09:47:16,605 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 879D155435E23ED39872C133BD33958E not found in internal, will be created
2025-08-26 09:47:16,625 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 1B84E8F1B5F338B4A2EFB8EE06542318 not found in internal, will be created
2025-08-26 09:47:16,641 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 56B8F016A02E34AFAE27A11A1D9452C0 not found in internal, will be created
2025-08-26 09:47:16,658 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key F169B6287B9B335DB852CC06E341EB32 not found in internal, will be created
2025-08-26 09:47:16,675 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 692B7A8786543F0EA6CF21A9A6B046BA not found in internal, will be created
2025-08-26 09:47:16,692 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key F2DB64859DCA357EAA1B738AC42E2AB5 not found in internal, will be created
2025-08-26 09:47:16,708 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 9D3D51B884833D9A94E755EE33E88ACE not found in internal, will be created
2025-08-26 09:47:16,723 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key A1F13EF9EFAB3834986114EFAAAC9072 not found in internal, will be created
2025-08-26 09:47:16,737 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 6F28DB5C9D3231CEBFBB9F8C94A78095 not found in internal, will be created
2025-08-26 09:47:16,750 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E99CB988044A309DAD7D87AD33E838D7 not found in internal, will be created
2025-08-26 09:47:16,763 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 5B3A7C5D97FF3110811B1DA308F7591E not found in internal, will be created
2025-08-26 09:47:16,777 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key ED386BECE1853A1986A9C3632CA03AD0 not found in internal, will be created
2025-08-26 09:47:16,791 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key EDC8D10EBCB03707A9EDA1C197C8D85E not found in internal, will be created
2025-08-26 09:47:16,806 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 8066E618432739C6B4D8EFEF7AE26A54 not found in internal, will be created
2025-08-26 09:47:16,824 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 6AA599DCFE353381870C8F8A508C69F2 not found in internal, will be created
2025-08-26 09:47:16,840 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key C51592A6540235B0BCF906AE55818183 not found in internal, will be created
2025-08-26 09:47:16,860 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 7D8053D2990A382E8031357E4AAFAB8A not found in internal, will be created
2025-08-26 09:47:16,878 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 82688BC0217C303D8AA8A8E2C8B87F41 not found in internal, will be created
2025-08-26 09:47:16,896 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 3B526D6AF46F38A3AF56C044CE694EA3 not found in internal, will be created
2025-08-26 09:47:16,911 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key F7BCF6AC67DC3C148F29E5BC717822A2 not found in internal, will be created
2025-08-26 09:47:16,926 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 6D4C715D8AED3DFDBC096F8B40FD45EA not found in internal, will be created
2025-08-26 09:47:16,940 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key AD2032B4F81B30739AB1856C3430A8F9 not found in internal, will be created
2025-08-26 09:47:16,954 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 42F9E12D1EBA3A4B8FA2D5E83326EF88 not found in internal, will be created
2025-08-26 09:47:16,968 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E0E83E044BD03EE0B7AEEDC2E509729B not found in internal, will be created
2025-08-26 09:47:16,983 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key C1A9541328683617956C330F976BA910 not found in internal, will be created
2025-08-26 09:47:16,998 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 458719980FF332A38402B49C6D9F9F4D not found in internal, will be created
2025-08-26 09:47:17,011 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 2FE16C1D89CD30FDB49E3395A9BF4759 not found in internal, will be created
2025-08-26 09:47:17,026 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 32DD5B2519293CF8A0DA6C41EC881734 not found in internal, will be created
2025-08-26 09:47:17,042 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 3126D77FA7C935F5BBB1AC577B9458EC not found in internal, will be created
2025-08-26 09:47:17,057 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key B97B5FC6BEE8370BA58DC3840CA5E3B2 not found in internal, will be created
2025-08-26 09:47:17,072 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 0AE7F2E84B0730969507E02FCA4C7A20 not found in internal, will be created
2025-08-26 09:47:17,088 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 3B63C6F374E73030A61DFBB49F363AA8 not found in internal, will be created
2025-08-26 09:47:17,105 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 4A5B873BAC133137917B0CB053307089 not found in internal, will be created
2025-08-26 09:47:17,121 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 6CE230640981315D81782EFBA9B8C91E not found in internal, will be created
2025-08-26 09:47:17,139 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key E5168FB48E9E35D8B56DEE6A32BA1A94 not found in internal, will be created
2025-08-26 09:47:17,154 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 43D7067C42483D29A0B58ECF5F82BCB7 not found in internal, will be created
2025-08-26 09:47:17,167 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key 6E104C33FAC53E5DB28F06BCFC20874B not found in internal, will be created
2025-08-26 09:47:17,182 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:977] - INFO: Cloud resource with key EE27DA2327BF334593788A647BAAF83C not found in internal, will be created
2025-08-26 09:47:17,183 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:3700] - INFO: Create operations (317) exceed limit (1) for model CLOUD_VM_NOVA in cloud_to_internal direction, limiting
2025-08-26 09:47:17,183 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:879] - INFO: Batch creating 1 items for model: CLOUD_VM_NOVA
2025-08-26 09:47:17,193 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 200
2025-08-26 09:47:17,193 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:3255] - INFO: operating internal api result: �����ɹ�
2025-08-26 09:47:17,194 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\record_config.py[line:158] - WARNING: Count mismatch: total_records=317, calculated_total=1 (success=1, failed=0, skipped=0)
2025-08-26 09:47:17,195 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\record_config.py[line:158] - WARNING: Count mismatch: total_records=317, calculated_total=1 (success=1, failed=0, skipped=0)
2025-08-26 09:47:17,195 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4722] - INFO: ͬ��ͳ��ժҪ - ģ��: CLOUD_VM_NOVA
2025-08-26 09:47:17,195 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4723] - INFO:   �ܼ�¼��: 317
2025-08-26 09:47:17,195 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4724] - INFO:   �ɹ�: 1 (����: 1, ����: 0, ɾ��: 0)
2025-08-26 09:47:17,196 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4725] - INFO:   ʧ��: 0
2025-08-26 09:47:17,196 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4726] - INFO:   ����: 0
2025-08-26 09:47:17,196 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4727] - INFO:   �ɹ���: 0.3%
2025-08-26 09:47:17,196 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4728] - INFO:   ͳ��һ����: FAIL
2025-08-26 09:47:17,197 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4731] - WARNING: ��⵽ͳ�����ݲ�һ�£�����ͬ���߼�
2025-08-26 09:47:17,197 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:901] - INFO: Completed syncing from cloud to internal for model: CLOUD_VM_NOVA
2025-08-26 09:47:24,470 - 21732-Thread-7 - E:\File\repository\xycmdbsync\src\record_config.py[line:472] - INFO: Saved sync record 7a771ad1-263c-4847-9cb0-ec61942da870 for model CLOUD_VM_NOVA to database
2025-08-26 09:49:34,094 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:324] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-08-26 09:49:34,094 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:329] - INFO: Using specified data source: source_1
2025-08-26 09:49:34,095 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:420] - ERROR: Error fetching cloud-side data for model CLOUD_VM_NOVA: rawData illegal
2025-08-26 09:49:34,096 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:514] - INFO: Fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-26 09:49:34,096 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:538] - INFO: Using cached data for model: CLOUD_VM_NOVA
2025-08-26 09:49:34,096 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:616] - INFO: Completed fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-26 09:49:34,097 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:245] - INFO: ��ʼͬ��ģ��: CLOUD_VM_NOVA
2025-08-26 09:49:34,104 - 21732-Thread-16 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:49:34,104 - 21732-Thread-16 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:49:34,106 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:49:34,107 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 1/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:49:34,107 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:49:39,114 - 21732-Thread-17 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:49:39,115 - 21732-Thread-17 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:49:39,117 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:49:39,118 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 2/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:49:39,118 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:49:44,125 - 21732-Thread-18 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:49:44,125 - 21732-Thread-18 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:49:44,127 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:49:44,128 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 3/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:49:44,128 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:49:49,136 - 21732-Thread-19 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:49:49,137 - 21732-Thread-19 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:49:49,139 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:49:49,140 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 4/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:49:49,140 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2160] - ERROR: HTTP���󣬲�������: 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:49:49,140 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4754] - ERROR: ��ȡ�ڲ�����ʧ��: API���÷���None�����������糬ʱ�����񲻿��û���֤ʧ�ܣ�
2025-08-26 09:49:49,141 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:681] - INFO: Synchronizing model: CLOUD_VM_NOVA
2025-08-26 09:49:49,141 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:694] - INFO: Using historical data for model CLOUD_VM_NOVA from previous sync cycle
2025-08-26 09:49:49,141 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:790] - INFO: Processing data from source source_1 for model CLOUD_VM_NOVA
2025-08-26 09:49:49,142 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2753] - INFO: Assembling data for model CLOUD_VM_NOVA
2025-08-26 09:49:49,987 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:812] - INFO: Syncing from cloud to internal for model: CLOUD_VM_NOVA
2025-08-26 09:49:54,236 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4722] - INFO: ͬ��ͳ��ժҪ - ģ��: CLOUD_VM_NOVA
2025-08-26 09:49:54,236 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4723] - INFO:   �ܼ�¼��: 317
2025-08-26 09:49:54,237 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4724] - INFO:   �ɹ�: 0 (����: 0, ����: 0, ɾ��: 0)
2025-08-26 09:49:54,237 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4725] - INFO:   ʧ��: 0
2025-08-26 09:49:54,237 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4726] - INFO:   ����: 317
2025-08-26 09:49:54,237 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4727] - INFO:   �ɹ���: 0.0%
2025-08-26 09:49:54,237 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4728] - INFO:   ͳ��һ����: PASS
2025-08-26 09:49:54,238 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:901] - INFO: Completed syncing from cloud to internal for model: CLOUD_VM_NOVA
2025-08-26 09:49:57,660 - 21732-Thread-15 - E:\File\repository\xycmdbsync\src\record_config.py[line:472] - INFO: Saved sync record 450ef457-d71d-4426-b056-7ddb7cf45e6f for model CLOUD_VM_NOVA to database
2025-08-26 09:52:14,123 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:324] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-08-26 09:52:14,124 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:329] - INFO: Using specified data source: source_1
2025-08-26 09:52:14,125 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:420] - ERROR: Error fetching cloud-side data for model CLOUD_VM_NOVA: rawData illegal
2025-08-26 09:52:14,127 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:514] - INFO: Fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-26 09:52:14,128 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:538] - INFO: Using cached data for model: CLOUD_VM_NOVA
2025-08-26 09:52:14,128 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:616] - INFO: Completed fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-26 09:52:14,129 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:245] - INFO: ��ʼͬ��ģ��: CLOUD_VM_NOVA
2025-08-26 09:52:14,141 - 21732-Thread-21 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:52:14,142 - 21732-Thread-21 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:52:14,147 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.02s, ״̬��: 404
2025-08-26 09:52:14,149 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 1/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:52:14,149 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:52:19,158 - 21732-Thread-22 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:52:19,159 - 21732-Thread-22 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:52:19,161 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:52:19,161 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 2/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:52:19,161 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:52:24,168 - 21732-Thread-23 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:52:24,169 - 21732-Thread-23 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:52:24,171 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:52:24,171 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 3/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:52:24,171 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:52:29,178 - 21732-Thread-24 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:52:29,179 - 21732-Thread-24 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:52:29,181 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:52:29,181 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 4/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:52:29,181 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2160] - ERROR: HTTP���󣬲�������: 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:52:29,182 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4754] - ERROR: ��ȡ�ڲ�����ʧ��: API���÷���None�����������糬ʱ�����񲻿��û���֤ʧ�ܣ�
2025-08-26 09:52:29,182 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:681] - INFO: Synchronizing model: CLOUD_VM_NOVA
2025-08-26 09:52:29,183 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:694] - INFO: Using historical data for model CLOUD_VM_NOVA from previous sync cycle
2025-08-26 09:52:29,184 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:790] - INFO: Processing data from source source_1 for model CLOUD_VM_NOVA
2025-08-26 09:52:29,184 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2753] - INFO: Assembling data for model CLOUD_VM_NOVA
2025-08-26 09:52:30,027 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:812] - INFO: Syncing from cloud to internal for model: CLOUD_VM_NOVA
2025-08-26 09:52:33,952 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4722] - INFO: ͬ��ͳ��ժҪ - ģ��: CLOUD_VM_NOVA
2025-08-26 09:52:33,952 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4723] - INFO:   �ܼ�¼��: 317
2025-08-26 09:52:33,952 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4724] - INFO:   �ɹ�: 0 (����: 0, ����: 0, ɾ��: 0)
2025-08-26 09:52:33,952 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4725] - INFO:   ʧ��: 0
2025-08-26 09:52:33,953 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4726] - INFO:   ����: 317
2025-08-26 09:52:33,953 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4727] - INFO:   �ɹ���: 0.0%
2025-08-26 09:52:33,953 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4728] - INFO:   ͳ��һ����: PASS
2025-08-26 09:52:33,953 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:901] - INFO: Completed syncing from cloud to internal for model: CLOUD_VM_NOVA
2025-08-26 09:52:37,622 - 21732-Thread-20 - E:\File\repository\xycmdbsync\src\record_config.py[line:472] - INFO: Saved sync record 5a64c512-07bd-4578-88ca-cd5b7d1f5b08 for model CLOUD_VM_NOVA to database
2025-08-26 09:53:17,679 - 13896-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-26 09:53:17,680 - 13896-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-26 09:53:17,681 - 13896-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-26 09:53:17,682 - 13896-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-26 09:53:17,683 - 13896-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-26 09:53:17,683 - 13896-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-26 09:53:17,837 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-26 09:53:17,947 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-26 09:53:17,956 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-26 09:53:17,956 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-26 09:53:17,971 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-26 09:53:18,216 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-26 09:53:18,296 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-26 09:53:18,622 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-26 09:53:18,749 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-26 09:53:18,898 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-26 09:53:18,995 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-26 09:53:19,200 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-26 09:53:19,355 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-26 09:53:19,513 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-26 09:53:19,692 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-26 09:53:19,938 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-26 09:53:20,113 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-26 09:53:20,289 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-26 09:53:20,462 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-26 09:53:20,565 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-26 09:53:20,916 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-26 09:53:20,987 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-26 09:53:21,061 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-26 09:53:21,303 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-26 09:53:21,432 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-26 09:53:21,727 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-26 09:53:21,875 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-26 09:53:22,054 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-26 09:53:22,282 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-26 09:53:22,481 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-26 09:53:22,676 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-26 09:53:22,862 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-26 09:53:22,949 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-26 09:53:23,043 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-26 09:53:23,130 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-26 09:53:23,266 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-26 09:53:23,406 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-26 09:53:23,533 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-26 09:53:23,839 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-26 09:53:24,126 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-26 09:53:24,303 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-26 09:53:24,459 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-26 09:53:24,574 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-26 09:53:24,723 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-26 09:53:24,796 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-26 09:53:24,863 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-26 09:53:24,942 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-26 09:53:25,094 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-26 09:53:25,173 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-26 09:53:25,278 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-26 09:53:25,375 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-26 09:53:25,412 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-26 09:53:25,476 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-26 09:53:25,655 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-26 09:53:25,862 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-26 09:53:25,916 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-26 09:53:25,978 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-26 09:53:26,034 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-26 09:53:26,094 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-26 09:53:26,129 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-26 09:53:26,189 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-26 09:53:26,249 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-26 09:53:26,326 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-26 09:53:26,540 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-26 09:53:26,892 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-26 09:53:26,893 - 13896-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-26 09:53:27,244 - 13896-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-26 09:53:27,250 - 13896-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-26 09:53:27,250 - 13896-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-26 09:53:27,251 - 13896-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-26 09:53:27,251 - 13896-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-26 09:53:27,252 - 13896-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-26 09:53:27,254 - 13896-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:231] - INFO: Loaded internal-side data for model CLOUD_VM_NOVA from cache
2025-08-26 09:53:27,338 - 13896-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:245] - INFO: Loaded historical data for model CLOUD_VM_NOVA from cache
2025-08-26 09:53:27,356 - 13896-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:356] - INFO: Sync record database tables initialized successfully
2025-08-26 09:53:27,382 - 13896-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:356] - INFO: Sync record database tables initialized successfully
2025-08-26 09:53:27,408 - 13896-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-26 09:53:27,409 - 13896-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=30����, ��ʼ=00:00
2025-08-26 09:53:27,410 - 13896-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30']
2025-08-26 09:53:27,411 - 13896-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-26 09:53:27,412 - 13896-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ30����ִ��һ�Σ���00:00��ʼ
2025-08-26 09:53:27,412 - 13896-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:30, 01:00, 01:30, 02:00, 02:30, 03:00, 03:30, 04:00, 04:30, 05:00, 05:30, 06:00, 06:30, 07:00, 07:30, 08:00, 08:30, 09:00, 09:30, 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00, 13:30, 14:00, 14:30, 15:00, 15:30, 16:00, 16:30, 17:00, 17:30, 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30, 23:00, 23:30
2025-08-26 09:53:27,413 - 13896-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 48 ��
2025-08-26 09:53:27,414 - 13896-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-26 10:00:00, ���뻹�� 393.00 �룬������...
2025-08-26 09:53:31,840 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:324] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-08-26 09:53:31,841 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:329] - INFO: Using specified data source: source_1
2025-08-26 09:53:31,842 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:361] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-08-26 09:53:31,843 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:378] - INFO: Fetching data for model CLOUD_VM_NOVA from source source_1
2025-08-26 09:53:32,208 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2356] - INFO: Password authentication successful for cloud_source_1
2025-08-26 09:53:32,570 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.36s, ״̬��: 200
2025-08-26 09:53:32,762 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.19s, ״̬��: 200
2025-08-26 09:53:32,950 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.19s, ״̬��: 200
2025-08-26 09:53:33,053 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:53:33,055 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:266] - INFO: Processing related models for CLOUD_VM_NOVA from source source_1
2025-08-26 09:53:33,055 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:277] - INFO: Processing related model R_Volume_MountOn_VM using method relation_table
2025-08-26 09:53:33,055 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2855] - INFO: Fetching relation table data for model CLOUD_VM_NOVA, related model R_Volume_MountOn_VM from source source_1
2025-08-26 09:53:33,162 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:53:33,260 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:53:33,363 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:53:33,450 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:33,537 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:53:33,639 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:53:33,721 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:53:33,811 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:33,893 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:53:33,986 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:34,104 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:53:34,190 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:34,276 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:53:34,390 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:53:34,477 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:34,563 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:34,666 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:53:34,777 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:53:34,870 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:34,975 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:53:35,068 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:35,167 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:53:35,280 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:53:35,368 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:35,454 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:35,536 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:53:35,625 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:35,731 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:53:35,839 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:53:35,926 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:36,010 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:53:36,097 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:36,184 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:36,290 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:53:36,385 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:53:36,469 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:53:36,554 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:53:36,667 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:53:36,794 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:53:36,918 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:53:37,048 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:53:37,176 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:53:37,296 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:53:37,425 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:53:37,539 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:53:37,657 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:53:37,790 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:53:37,907 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:53:38,008 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:53:38,050 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:285] - INFO: Fetched 381 records for related model R_Volume_MountOn_VM
2025-08-26 09:53:38,050 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:277] - INFO: Processing related model M_VMUsesFlavor using method relation_table
2025-08-26 09:53:38,051 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2855] - INFO: Fetching relation table data for model CLOUD_VM_NOVA, related model M_VMUsesFlavor from source source_1
2025-08-26 09:53:38,147 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:53:38,237 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:38,346 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:53:38,433 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:38,547 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:53:38,635 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:38,722 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:38,813 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:38,931 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:53:39,040 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:53:39,118 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:53:39,210 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:39,316 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:53:39,404 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:39,484 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:53:39,575 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:53:39,699 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:53:39,824 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:53:39,830 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:285] - INFO: Fetched 90 records for related model M_VMUsesFlavor
2025-08-26 09:53:39,830 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:411] - INFO: Completed fetching cloud-side data for model: CLOUD_VM_NOVA from source source_1 (found 317 records)
2025-08-26 09:53:39,831 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:420] - ERROR: Error fetching cloud-side data for model CLOUD_VM_NOVA: rawData illegal
2025-08-26 09:53:39,835 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:514] - INFO: Fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-26 09:53:39,835 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:538] - INFO: Using cached data for model: CLOUD_VM_NOVA
2025-08-26 09:53:39,835 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:616] - INFO: Completed fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-26 09:53:39,835 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:245] - INFO: ��ʼͬ��ģ��: CLOUD_VM_NOVA
2025-08-26 09:53:39,850 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2356] - INFO: Bearer authentication successful for internal_side
2025-08-26 09:53:39,867 - 13896-Thread-9 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:53:39,867 - 13896-Thread-9 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:53:39,870 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.02s, ״̬��: 404
2025-08-26 09:53:39,870 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 1/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:53:39,870 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:53:44,878 - 13896-Thread-10 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:53:44,879 - 13896-Thread-10 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:53:44,881 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:53:44,882 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 2/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:53:44,882 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:53:49,891 - 13896-Thread-11 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:53:49,891 - 13896-Thread-11 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:53:49,894 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:53:49,894 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 3/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:53:49,895 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:53:54,902 - 13896-Thread-12 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:53:54,903 - 13896-Thread-12 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:53:54,905 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:53:54,905 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 4/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:53:54,905 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2160] - ERROR: HTTP���󣬲�������: 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:53:54,906 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4754] - ERROR: ��ȡ�ڲ�����ʧ��: API���÷���None�����������糬ʱ�����񲻿��û���֤ʧ�ܣ�
2025-08-26 09:53:54,906 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:681] - INFO: Synchronizing model: CLOUD_VM_NOVA
2025-08-26 09:53:54,907 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:694] - INFO: Using historical data for model CLOUD_VM_NOVA from previous sync cycle
2025-08-26 09:53:54,908 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:790] - INFO: Processing data from source source_1 for model CLOUD_VM_NOVA
2025-08-26 09:53:54,909 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2753] - INFO: Assembling data for model CLOUD_VM_NOVA
2025-08-26 09:53:55,755 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:812] - INFO: Syncing from cloud to internal for model: CLOUD_VM_NOVA
2025-08-26 09:54:00,258 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4722] - INFO: ͬ��ͳ��ժҪ - ģ��: CLOUD_VM_NOVA
2025-08-26 09:54:00,258 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4723] - INFO:   �ܼ�¼��: 317
2025-08-26 09:54:00,258 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4724] - INFO:   �ɹ�: 0 (����: 0, ����: 0, ɾ��: 0)
2025-08-26 09:54:00,259 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4725] - INFO:   ʧ��: 0
2025-08-26 09:54:00,259 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4726] - INFO:   ����: 317
2025-08-26 09:54:00,259 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4727] - INFO:   �ɹ���: 0.0%
2025-08-26 09:54:00,259 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4728] - INFO:   ͳ��һ����: PASS
2025-08-26 09:54:00,259 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:901] - INFO: Completed syncing from cloud to internal for model: CLOUD_VM_NOVA
2025-08-26 09:54:03,594 - 13896-Thread-7 - E:\File\repository\xycmdbsync\src\record_config.py[line:472] - INFO: Saved sync record cc8be5cf-df8e-4aa2-a790-37cb2d0c17b2 for model CLOUD_VM_NOVA to database
2025-08-26 09:58:04,667 - 26384-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-26 09:58:04,669 - 26384-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-26 09:58:04,670 - 26384-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-26 09:58:04,672 - 26384-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-26 09:58:04,674 - 26384-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-26 09:58:04,675 - 26384-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-26 09:58:05,033 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-26 09:58:05,345 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-26 09:58:05,365 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-26 09:58:05,367 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-26 09:58:05,413 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-26 09:58:06,249 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-26 09:58:06,561 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-26 09:58:07,496 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-26 09:58:07,825 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-26 09:58:07,983 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-26 09:58:08,097 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-26 09:58:08,324 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-26 09:58:08,485 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-26 09:58:08,667 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-26 09:58:09,058 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-26 09:58:09,579 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-26 09:58:09,908 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-26 09:58:10,179 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-26 09:58:10,444 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-26 09:58:10,582 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-26 09:58:11,057 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-26 09:58:11,192 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-26 09:58:11,327 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-26 09:58:11,808 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-26 09:58:12,026 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-26 09:58:12,480 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-26 09:58:12,623 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-26 09:58:12,803 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-26 09:58:13,134 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-26 09:58:13,470 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-26 09:58:13,880 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-26 09:58:14,229 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-26 09:58:14,342 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-26 09:58:14,467 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-26 09:58:14,571 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-26 09:58:14,708 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-26 09:58:14,872 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-26 09:58:15,010 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-26 09:58:15,335 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-26 09:58:15,728 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-26 09:58:16,082 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-26 09:58:16,319 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-26 09:58:16,507 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-26 09:58:16,717 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-26 09:58:16,806 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-26 09:58:16,884 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-26 09:58:17,041 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-26 09:58:17,349 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-26 09:58:17,549 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-26 09:58:17,724 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-26 09:58:17,835 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-26 09:58:17,873 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-26 09:58:17,955 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-26 09:58:18,152 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-26 09:58:18,405 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-26 09:58:18,464 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-26 09:58:18,538 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-26 09:58:18,603 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-26 09:58:18,667 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-26 09:58:18,701 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-26 09:58:18,765 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-26 09:58:18,833 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-26 09:58:18,916 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-26 09:58:19,124 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-26 09:58:19,388 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-26 09:58:19,388 - 26384-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-26 09:58:19,522 - 26384-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-26 09:58:19,526 - 26384-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-26 09:58:19,527 - 26384-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-26 09:58:19,527 - 26384-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-26 09:58:19,527 - 26384-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-26 09:58:19,528 - 26384-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-26 09:58:19,529 - 26384-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:231] - INFO: Loaded internal-side data for model CLOUD_VM_NOVA from cache
2025-08-26 09:58:19,580 - 26384-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:245] - INFO: Loaded historical data for model CLOUD_VM_NOVA from cache
2025-08-26 09:58:19,592 - 26384-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:356] - INFO: Sync record database tables initialized successfully
2025-08-26 09:58:19,610 - 26384-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:356] - INFO: Sync record database tables initialized successfully
2025-08-26 09:58:19,637 - 26384-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-26 09:58:19,638 - 26384-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=30����, ��ʼ=00:00
2025-08-26 09:58:19,640 - 26384-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30']
2025-08-26 09:58:19,641 - 26384-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-26 09:58:19,641 - 26384-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ30����ִ��һ�Σ���00:00��ʼ
2025-08-26 09:58:19,642 - 26384-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:30, 01:00, 01:30, 02:00, 02:30, 03:00, 03:30, 04:00, 04:30, 05:00, 05:30, 06:00, 06:30, 07:00, 07:30, 08:00, 08:30, 09:00, 09:30, 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00, 13:30, 14:00, 14:30, 15:00, 15:30, 16:00, 16:30, 17:00, 17:30, 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30, 23:00, 23:30
2025-08-26 09:58:19,642 - 26384-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 48 ��
2025-08-26 09:58:19,643 - 26384-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-26 10:00:00, ���뻹�� 101.00 �룬������...
2025-08-26 09:58:58,486 - 9940-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:279] - INFO: ********************************************************************************
2025-08-26 09:58:58,488 - 9940-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:280] - INFO: ��־ϵͳ�ѳ�ʼ������־Ŀ¼: E:\File\repository\xycmdbsync\logs
2025-08-26 09:58:58,489 - 9940-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:281] - INFO: ��־ϵͳ�ѳ�ʼ������־��ת��СΪ: 300M
2025-08-26 09:58:58,490 - 9940-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:282] - INFO: ��־ϵͳ�ѳ�ʼ������־������СΪ50���ļ�
2025-08-26 09:58:58,491 - 9940-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:283] - INFO: ��־ϵͳ�ѳ�ʼ������ǰ��־����Ϊ��20
2025-08-26 09:58:58,493 - 9940-MainThread - E:\File\repository\xycmdbsync\src\logger_config.py[line:284] - INFO: ********************************************************************************
2025-08-26 09:58:58,693 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:44] - INFO: ��ʼ��������...
2025-08-26 09:58:58,869 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:69] - INFO: Loaded base config from E:\File\repository\xycmdbsync\config\base_config.yaml
2025-08-26 09:58:58,884 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:88] - INFO: Found cloud sources: source_1, source_2
2025-08-26 09:58:58,885 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for cloud_side from E:\File\repository\xycmdbsync\config\endpoints\cloud_side.yaml
2025-08-26 09:58:58,927 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:90] - INFO: Loaded endpoints for internal_side from E:\File\repository\xycmdbsync\config\endpoints\internal_side.yaml
2025-08-26 09:58:59,744 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:106] - INFO: Loaded transformers from E:\File\repository\xycmdbsync\config\transformers\common.yaml
2025-08-26 09:58:59,901 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_AZONE from E:\File\repository\xycmdbsync\config\models\cloud_azone.yaml
2025-08-26 09:59:00,418 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_BMS from E:\File\repository\xycmdbsync\config\models\cloud_bms.yaml
2025-08-26 09:59:00,624 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cce_cluster.yaml
2025-08-26 09:59:00,956 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_CONTAINER from E:\File\repository\xycmdbsync\config\models\cloud_cce_container.yaml
2025-08-26 09:59:01,212 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NAMESPACE from E:\File\repository\xycmdbsync\config\models\cloud_cce_namespace.yaml
2025-08-26 09:59:01,497 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_NODE from E:\File\repository\xycmdbsync\config\models\cloud_cce_node.yaml
2025-08-26 09:59:01,678 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_POD from E:\File\repository\xycmdbsync\config\models\cloud_cce_pod.yaml
2025-08-26 09:59:01,846 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CCE_SERVICE from E:\File\repository\xycmdbsync\config\models\cloud_cce_service.yaml
2025-08-26 09:59:02,199 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudCronJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_cronjob.yaml
2025-08-26 09:59:02,518 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDaemonset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_daemonset.yaml
2025-08-26 09:59:02,819 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudDeployment from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_deployment.yaml
2025-08-26 09:59:03,086 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudJob from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_job.yaml
2025-08-26 09:59:03,482 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CloudStatefulset from E:\File\repository\xycmdbsync\config\models\cloud_cce_workload_statefulset.yaml
2025-08-26 09:59:03,702 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_CLUSTER from E:\File\repository\xycmdbsync\config\models\cloud_cluster.yaml
2025-08-26 09:59:04,323 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_HOST from E:\File\repository\xycmdbsync\config\models\cloud_host.yaml
2025-08-26 09:59:04,444 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_OBS_BUCKET from E:\File\repository\xycmdbsync\config\models\cloud_obs_bucket.yaml
2025-08-26 09:59:04,566 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_SFS_SHARE from E:\File\repository\xycmdbsync\config\models\cloud_sfs_share.yaml
2025-08-26 09:59:04,879 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VM_NOVA from E:\File\repository\xycmdbsync\config\models\cloud_vm_nova.yaml
2025-08-26 09:59:04,998 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for CLOUD_VOLUME from E:\File\repository\xycmdbsync\config\models\cloud_volume.yaml
2025-08-26 09:59:05,372 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_kafka from E:\File\repository\xycmdbsync\config\models\middleware_kafka.yaml
2025-08-26 09:59:05,559 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_nacos from E:\File\repository\xycmdbsync\config\models\middleware_nacos.yaml
2025-08-26 09:59:05,779 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_redis from E:\File\repository\xycmdbsync\config\models\middleware_redis.yaml
2025-08-26 09:59:06,217 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_rocketmq from E:\File\repository\xycmdbsync\config\models\middleware_rocket_mq.yaml
2025-08-26 09:59:06,500 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_optical_switch from E:\File\repository\xycmdbsync\config\models\network_access_optical_switch.yaml
2025-08-26 09:59:06,731 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for access_switch from E:\File\repository\xycmdbsync\config\models\network_access_switch.yaml
2025-08-26 09:59:06,958 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for back_bone_router from E:\File\repository\xycmdbsync\config\models\network_backbone_router.yaml
2025-08-26 09:59:07,047 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_bandwidths from E:\File\repository\xycmdbsync\config\models\network_cloud_bandwidths.yaml
2025-08-26 09:59:07,155 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_floating_ips from E:\File\repository\xycmdbsync\config\models\network_cloud_floating_ips.yaml
2025-08-26 09:59:07,283 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_network from E:\File\repository\xycmdbsync\config\models\network_cloud_network.yaml
2025-08-26 09:59:07,482 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_port from E:\File\repository\xycmdbsync\config\models\network_cloud_port.yaml
2025-08-26 09:59:07,637 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_router from E:\File\repository\xycmdbsync\config\models\network_cloud_router.yaml
2025-08-26 09:59:07,776 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_subnet from E:\File\repository\xycmdbsync\config\models\network_cloud_subnet.yaml
2025-08-26 09:59:08,062 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_optical_switch from E:\File\repository\xycmdbsync\config\models\network_core_optical_switch.yaml
2025-08-26 09:59:08,292 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for core_switch from E:\File\repository\xycmdbsync\config\models\network_core_switch.yaml
2025-08-26 09:59:08,522 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for network_device_port from E:\File\repository\xycmdbsync\config\models\network_device_port.yaml
2025-08-26 09:59:08,742 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall from E:\File\repository\xycmdbsync\config\models\network_fire_wall.yaml
2025-08-26 09:59:08,907 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for firewall_policy_log from E:\File\repository\xycmdbsync\config\models\network_fire_wall_policy_log.yaml
2025-08-26 09:59:09,219 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for land_balance_device from E:\File\repository\xycmdbsync\config\models\network_land_balance_device.yaml
2025-08-26 09:59:09,338 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool.yaml
2025-08-26 09:59:09,489 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_pool_men from E:\File\repository\xycmdbsync\config\models\network_load_balance_pool_men.yaml
2025-08-26 09:59:09,671 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for load_balance_service from E:\File\repository\xycmdbsync\config\models\network_load_balance_service.yaml
2025-08-26 09:59:09,883 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for non_backbone_router from E:\File\repository\xycmdbsync\config\models\network_non_backbone_router.yaml
2025-08-26 09:59:10,008 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for sys_deviceLink from E:\File\repository\xycmdbsync\config\models\network_sys_devicelink.yaml
2025-08-26 09:59:10,139 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for cloud_waf_engine from E:\File\repository\xycmdbsync\config\models\secure_cloud_waf_engine.yaml
2025-08-26 09:59:10,279 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_BusinessRegion from E:\File\repository\xycmdbsync\config\models\sys_businessregion.yaml
2025-08-26 09:59:10,322 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DataCenter from E:\File\repository\xycmdbsync\config\models\sys_datacenter.yaml
2025-08-26 09:59:10,394 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_DistributedStorage from E:\File\repository\xycmdbsync\config\models\sys_distributedstorage.yaml
2025-08-26 09:59:10,623 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_LUN from E:\File\repository\xycmdbsync\config\models\sys_lun.yaml
2025-08-26 09:59:10,972 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_PhysicalServer from E:\File\repository\xycmdbsync\config\models\sys_physicalserver.yaml
2025-08-26 09:59:11,065 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack from E:\File\repository\xycmdbsync\config\models\sys_rack.yaml
2025-08-26 09:59:11,156 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_IntegratedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_integratedcabinet.yaml
2025-08-26 09:59:11,244 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Rack_ShieldedCabinet from E:\File\repository\xycmdbsync\config\models\sys_rack_shieldedcabinet.yaml
2025-08-26 09:59:11,333 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_ResourcePool from E:\File\repository\xycmdbsync\config\models\sys_resourcepool.yaml
2025-08-26 09:59:11,374 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_Room from E:\File\repository\xycmdbsync\config\models\sys_room.yaml
2025-08-26 09:59:11,439 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageCluster from E:\File\repository\xycmdbsync\config\models\sys_storagecluster.yaml
2025-08-26 09:59:11,520 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorageNode from E:\File\repository\xycmdbsync\config\models\sys_storagenode.yaml
2025-08-26 09:59:11,604 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StoragePool from E:\File\repository\xycmdbsync\config\models\sys_storagepool.yaml
2025-08-26 09:59:11,807 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_StorDevice from E:\File\repository\xycmdbsync\config\models\sys_stordevice.yaml
2025-08-26 09:59:12,043 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:139] - INFO: Loaded model config for SYS_X86Server from E:\File\repository\xycmdbsync\config\models\sys_x86server.yaml
2025-08-26 09:59:12,044 - 9940-MainThread - E:\File\repository\xycmdbsync\src\config_loader.py[line:50] - INFO: ���ü������
2025-08-26 09:59:12,167 - 9940-MainThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:45] - INFO: MySQL���ӳس�ʼ���ɹ�����С: 5
2025-08-26 09:59:12,171 - 9940-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_1
2025-08-26 09:59:12,171 - 9940-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:122] - INFO: Initialized cloud API client for source: source_2
2025-08-26 09:59:12,172 - 9940-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:136] - INFO: Initialized cloud API client for source: source_1
2025-08-26 09:59:12,172 - 9940-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:150] - INFO: Initialized internal API client
2025-08-26 09:59:12,172 - 9940-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:212] - INFO: Loading data from cache
2025-08-26 09:59:12,174 - 9940-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:231] - INFO: Loaded internal-side data for model CLOUD_VM_NOVA from cache
2025-08-26 09:59:12,216 - 9940-MainThread - E:\File\repository\xycmdbsync\src\sync_manager.py[line:245] - INFO: Loaded historical data for model CLOUD_VM_NOVA from cache
2025-08-26 09:59:12,227 - 9940-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:356] - INFO: Sync record database tables initialized successfully
2025-08-26 09:59:12,242 - 9940-MainThread - E:\File\repository\xycmdbsync\src\record_config.py[line:356] - INFO: Sync record database tables initialized successfully
2025-08-26 09:59:12,261 - 9940-RestApiThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:47] - INFO: �����߳�: restful_api��Դ�ϱ��ӿ�.
2025-08-26 09:59:12,262 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:208] - INFO: ������(����)��ʼ�����: ���=30����, ��ʼ=00:00
2025-08-26 09:59:12,263 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\scheduler.py[line:209] - INFO: ִ��ʱ���: ['00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30']
2025-08-26 09:59:12,264 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:81] - INFO: �����߳�: async_model��ʱ����ͬ������
2025-08-26 09:59:12,265 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:82] - INFO: ��������: ÿ30����ִ��һ�Σ���00:00��ʼ
2025-08-26 09:59:12,265 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:83] - INFO: ִ��ʱ���: 00:00, 00:30, 01:00, 01:30, 02:00, 02:30, 03:00, 03:30, 04:00, 04:30, 05:00, 05:30, 06:00, 06:30, 07:00, 07:30, 08:00, 08:30, 09:00, 09:30, 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00, 13:30, 14:00, 14:30, 15:00, 15:30, 16:00, 16:30, 17:00, 17:30, 18:00, 18:30, 19:00, 19:30, 20:00, 20:30, 21:00, 21:30, 22:00, 22:30, 23:00, 23:30
2025-08-26 09:59:12,265 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:84] - INFO: ÿ��ִ�д���: 48 ��
2025-08-26 09:59:12,266 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-26 10:00:00, ���뻹�� 48.00 �룬������...
2025-08-26 09:59:17,545 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:324] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-08-26 09:59:17,545 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:329] - INFO: Using specified data source: source_1
2025-08-26 09:59:17,546 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:361] - INFO: Fetching cloud-side data for model: CLOUD_VM_NOVA
2025-08-26 09:59:17,546 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:378] - INFO: Fetching data for model CLOUD_VM_NOVA from source source_1
2025-08-26 09:59:17,912 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2356] - INFO: Password authentication successful for cloud_source_1
2025-08-26 09:59:18,265 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.35s, ״̬��: 200
2025-08-26 09:59:18,486 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.22s, ״̬��: 200
2025-08-26 09:59:18,691 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.20s, ״̬��: 200
2025-08-26 09:59:18,849 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.16s, ״̬��: 200
2025-08-26 09:59:18,850 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:266] - INFO: Processing related models for CLOUD_VM_NOVA from source source_1
2025-08-26 09:59:18,850 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:277] - INFO: Processing related model R_Volume_MountOn_VM using method relation_table
2025-08-26 09:59:18,850 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2855] - INFO: Fetching relation table data for model CLOUD_VM_NOVA, related model R_Volume_MountOn_VM from source source_1
2025-08-26 09:59:19,011 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.16s, ״̬��: 200
2025-08-26 09:59:19,133 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:59:19,277 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:59:19,366 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:19,455 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:19,555 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:19,645 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:19,763 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:59:19,857 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:19,980 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:59:20,086 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:20,186 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:20,287 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:20,393 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:59:20,505 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:59:20,586 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:59:20,677 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:20,767 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:20,852 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:59:20,947 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:21,085 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:59:21,188 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:21,285 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:21,402 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:59:21,495 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:21,598 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:21,684 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:59:21,787 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:21,874 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:21,980 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:22,075 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:22,160 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:59:22,249 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:22,337 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:22,426 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:22,510 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.08s, ״̬��: 200
2025-08-26 09:59:22,612 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:22,745 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:59:22,878 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:59:23,006 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:59:23,142 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:59:23,265 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:59:23,396 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:59:23,520 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:59:23,638 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:59:23,777 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:59:23,919 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.14s, ״̬��: 200
2025-08-26 09:59:24,048 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:59:24,167 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:59:24,203 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:285] - INFO: Fetched 381 records for related model R_Volume_MountOn_VM
2025-08-26 09:59:24,204 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:277] - INFO: Processing related model M_VMUsesFlavor using method relation_table
2025-08-26 09:59:24,204 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2855] - INFO: Fetching relation table data for model CLOUD_VM_NOVA, related model M_VMUsesFlavor from source source_1
2025-08-26 09:59:24,293 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:24,385 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:24,478 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:24,578 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:24,697 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:59:24,786 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:24,889 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:24,984 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:25,082 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:25,195 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:59:25,317 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.12s, ״̬��: 200
2025-08-26 09:59:25,420 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.10s, ״̬��: 200
2025-08-26 09:59:25,516 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:25,630 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.11s, ״̬��: 200
2025-08-26 09:59:25,757 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.13s, ״̬��: 200
2025-08-26 09:59:25,849 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.09s, ״̬��: 200
2025-08-26 09:59:26,021 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.17s, ״̬��: 200
2025-08-26 09:59:26,232 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:1964] - INFO: �ƶ�API���ú�ʱ: 0.21s, ״̬��: 200
2025-08-26 09:59:26,238 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:285] - INFO: Fetched 90 records for related model M_VMUsesFlavor
2025-08-26 09:59:26,238 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:411] - INFO: Completed fetching cloud-side data for model: CLOUD_VM_NOVA from source source_1 (found 317 records)
2025-08-26 09:59:26,239 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:420] - ERROR: Error fetching cloud-side data for model CLOUD_VM_NOVA: rawData illegal
2025-08-26 09:59:26,243 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:514] - INFO: Fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-26 09:59:26,243 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:538] - INFO: Using cached data for model: CLOUD_VM_NOVA
2025-08-26 09:59:26,243 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:616] - INFO: Completed fetching internal-side data for model: CLOUD_VM_NOVA
2025-08-26 09:59:26,243 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:245] - INFO: ��ʼͬ��ģ��: CLOUD_VM_NOVA
2025-08-26 09:59:26,258 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2356] - INFO: Bearer authentication successful for internal_side
2025-08-26 09:59:26,266 - 9940-Thread-9 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:59:26,266 - 9940-Thread-9 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:59:26,268 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:59:26,269 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 1/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:59:26,270 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:59:31,279 - 9940-Thread-10 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:59:31,279 - 9940-Thread-10 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:59:31,282 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 404
2025-08-26 09:59:31,283 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 2/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:59:31,283 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:59:36,297 - 9940-Thread-11 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:59:36,298 - 9940-Thread-11 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:59:36,301 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.02s, ״̬��: 404
2025-08-26 09:59:36,302 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 3/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:59:36,302 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2156] - INFO: ���������󣬵ȴ� 5 �������...
2025-08-26 09:59:41,320 - 9940-Thread-12 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:318] - INFO: ��ȡ��̬����: ApplicationSystem
2025-08-26 09:59:41,320 - 9940-Thread-12 - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:327] - ERROR: �����ļ�������: E:\File\repository\xycmdbsync\internalApiData\ApplicationSystem.json
2025-08-26 09:59:41,322 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.02s, ״̬��: 404
2025-08-26 09:59:41,323 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2141] - WARNING: HTTP���� 404 (���� 4/4): 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:59:41,323 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2160] - ERROR: HTTP���󣬲�������: 404 Client Error: NOT FOUND for url: http://127.0.0.1:23601/cmdb/api/class/getCommonDynamicData
2025-08-26 09:59:41,323 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4754] - ERROR: ��ȡ�ڲ�����ʧ��: API���÷���None�����������糬ʱ�����񲻿��û���֤ʧ�ܣ�
2025-08-26 09:59:41,324 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:681] - INFO: Synchronizing model: CLOUD_VM_NOVA
2025-08-26 09:59:41,324 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:694] - INFO: Using historical data for model CLOUD_VM_NOVA from previous sync cycle
2025-08-26 09:59:41,325 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:790] - INFO: Processing data from source source_1 for model CLOUD_VM_NOVA
2025-08-26 09:59:41,325 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2753] - INFO: Assembling data for model CLOUD_VM_NOVA
2025-08-26 09:59:42,132 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:812] - INFO: Syncing from cloud to internal for model: CLOUD_VM_NOVA
2025-08-26 09:59:42,153 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:964] - INFO: Item with key 49C0566302E537E48F98A9380B9EFB6E has changes, will be updated
2025-08-26 09:59:42,166 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:964] - INFO: Item with key 71365B02B48533C0A7C7A2B4D4398989 has changes, will be updated
2025-08-26 09:59:46,264 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:3700] - INFO: Update operations (2) exceed limit (1) for model CLOUD_VM_NOVA in cloud_to_internal direction, limiting
2025-08-26 09:59:46,265 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:869] - INFO: Batch updating 1 items for model: CLOUD_VM_NOVA
2025-08-26 09:59:46,273 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:2104] - INFO: API���ú�ʱ: 0.01s, ״̬��: 200
2025-08-26 09:59:46,274 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:3255] - INFO: operating internal api result: �����ɹ�
2025-08-26 09:59:46,274 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\record_config.py[line:158] - WARNING: Count mismatch: total_records=317, calculated_total=316 (success=1, failed=0, skipped=315)
2025-08-26 09:59:46,275 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\record_config.py[line:158] - WARNING: Count mismatch: total_records=317, calculated_total=316 (success=1, failed=0, skipped=315)
2025-08-26 09:59:46,275 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4722] - INFO: ͬ��ͳ��ժҪ - ģ��: CLOUD_VM_NOVA
2025-08-26 09:59:46,275 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4723] - INFO:   �ܼ�¼��: 317
2025-08-26 09:59:46,276 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4724] - INFO:   �ɹ�: 1 (����: 0, ����: 1, ɾ��: 0)
2025-08-26 09:59:46,276 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4725] - INFO:   ʧ��: 0
2025-08-26 09:59:46,276 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4726] - INFO:   ����: 315
2025-08-26 09:59:46,276 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4727] - INFO:   �ɹ���: 0.3%
2025-08-26 09:59:46,276 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4728] - INFO:   ͳ��һ����: FAIL
2025-08-26 09:59:46,276 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:4731] - WARNING: ��⵽ͳ�����ݲ�һ�£�����ͬ���߼�
2025-08-26 09:59:46,277 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\sync_manager.py[line:901] - INFO: Completed syncing from cloud to internal for model: CLOUD_VM_NOVA
2025-08-26 09:59:48,942 - 9940-Thread-7 - E:\File\repository\xycmdbsync\src\record_config.py[line:472] - INFO: Saved sync record 2cdd5f4c-806d-486c-a288-2f83dd356af3 for model CLOUD_VM_NOVA to database
2025-08-26 10:00:00,280 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-26 10:00:00,281 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-26 10:00:00,281 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-26 10:00:00,287 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-26 10:00:00,290 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-26 10:00:00,290 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-26 10:00:00,392 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-26 10:00:00,392 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-26 10:30:00, ���뻹�� 1800.00 �룬������...
2025-08-26 10:30:00,789 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:161] - INFO: ****************����ִ��ͬ�� (30���Ӽ��)**********************
2025-08-26 10:30:00,789 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:161] - WARNING: ���֤Ŀ¼������: E:\File\repository\xycmdbsync\config\license
2025-08-26 10:30:00,789 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:164] - INFO: ���Դ����ݿ��ȡ���֤��Ϣ
2025-08-26 10:30:00,792 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:73] - INFO: Connection to MySQL DB successful
2025-08-26 10:30:00,794 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\database\database_utils.py[line:88] - INFO: MySQL connection is closed
2025-08-26 10:30:00,794 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\license\licensecheck.py[line:170] - INFO: �ɹ������ݿ��ȡ���֤��Ϣ: XYCMDB
2025-08-26 10:30:00,811 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:165] - INFO: **************License�ѹ���*******************
2025-08-26 10:30:00,812 - 9940-AsyncModelThread - E:\File\repository\xycmdbsync\src\cmdb_sync_driver.py[line:109] - INFO: ��һ��ͬ��ʱ��: 2025-08-26 11:00:00, ���뻹�� 1800.00 �룬������...
